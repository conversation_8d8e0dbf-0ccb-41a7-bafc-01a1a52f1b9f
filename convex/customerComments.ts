import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { nanoid } from "nanoid";

// Add a customer comment to a shared project
export const addComment = mutation({
  args: {
    projectId: v.id("projects"),
    sharedId: v.string(),
    customerName: v.string(),
    customerEmail: v.optional(v.string()),
    comment: v.string(),
    ipAddress: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (!project.isPubliclyShared) {
      throw new Error("Dette prosjektet er ikke delt offentlig");
    }

    // Check if sharing has expired
    if (project.shareSettings?.expiresAt && project.shareSettings.expiresAt < Date.now()) {
      throw new Error("Delingslinken har utløpt");
    }

    // Check if comments are allowed
    if (!project.shareSettings?.allowCustomerComments) {
      throw new Error("Kommentarer er ikke tillatt for dette prosjektet");
    }

    // Validate input
    if (!args.customerName.trim()) {
      throw new Error("Navn er påkrevd");
    }

    if (!args.comment.trim()) {
      throw new Error("Kommentar kan ikke være tom");
    }

    if (args.comment.length > 1000) {
      throw new Error("Kommentar kan ikke være lengre enn 1000 tegn");
    }

    // Add the comment (published immediately)
    const commentId = await ctx.db.insert("customerComments", {
      projectId: args.projectId,
      sharedId: args.sharedId,
      // Legacy fields
      customerName: args.customerName.trim(),
      customerEmail: args.customerEmail?.trim(),
      comment: args.comment.trim(),
      createdAt: Date.now(),
      ipAddress: args.ipAddress,
      // New required fields (for backward compatibility)
      threadId: nanoid(12), // Generate a thread ID for legacy comments
      parentCommentId: undefined,
      isRootComment: true,
      authorType: "customer" as const,
      contractorId: undefined,
      message: args.comment.trim() // Duplicate the comment as message
    });

    return { success: true, commentId };
  }
});

// Get comments for a project (for contractors)
export const getByProject = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project belongs to the user
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til kommentarer for dette prosjektet");
    }

    // Get all comments for the project
    const comments = await ctx.db
      .query("customerComments")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .order("desc")
      .collect();

    // Filter to only return true legacy comments (not modern comments created via addComment)
    const legacyComments = comments.filter(comment =>
      // Legacy comments have contractorReply OR have comment field but were created before threaded system
      comment.contractorReply !== undefined ||
      (comment.comment !== undefined && comment.threadId === undefined)
    );

    return legacyComments;
  }
});

// Get all comments for a project (for public view)
export const getForPublicView = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return [];
    }

    if (!project.isPubliclyShared) {
      return [];
    }

    // Check if sharing has expired
    if (project.shareSettings?.expiresAt && project.shareSettings.expiresAt < Date.now()) {
      return [];
    }

    // Get only LEGACY comments (those with contractorReply field or comment field but no threadId)
    const comments = await ctx.db
      .query("customerComments")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .order("desc")
      .collect();

    // Filter to only return true legacy comments (not modern comments created via addComment)
    const legacyComments = comments.filter(comment =>
      // Legacy comments have contractorReply OR have comment field but were created before threaded system
      comment.contractorReply !== undefined ||
      (comment.comment !== undefined && comment.threadId === undefined)
    );

    return legacyComments;
  }
});

// Add a contractor reply to a customer comment
export const addReply = mutation({
  args: {
    commentId: v.id("customerComments"),
    userId: v.string(),
    replyMessage: v.string()
  },
  handler: async (ctx, args) => {
    // Get the comment
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Kommentar ikke funnet");
    }

    // Verify the project belongs to the user
    const project = await ctx.db.get(comment.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å svare på kommentarer for dette prosjektet");
    }

    // Validate reply message
    if (!args.replyMessage.trim()) {
      throw new Error("Svar kan ikke være tomt");
    }

    if (args.replyMessage.length > 1000) {
      throw new Error("Svar kan ikke være lengre enn 1000 tegn");
    }

    // Add the reply
    await ctx.db.patch(args.commentId, {
      contractorReply: {
        message: args.replyMessage.trim(),
        repliedAt: Date.now(),
        repliedBy: args.userId
      }
    });

    return { success: true };
  }
});

// Remove a contractor reply from a comment
export const removeReply = mutation({
  args: {
    commentId: v.id("customerComments"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the comment
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Kommentar ikke funnet");
    }

    // Verify the project belongs to the user
    const project = await ctx.db.get(comment.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å fjerne svar for dette prosjektet");
    }

    // Remove the reply
    await ctx.db.patch(args.commentId, {
      contractorReply: undefined
    });

    return { success: true };
  }
});

// Delete a customer comment
export const deleteComment = mutation({
  args: {
    commentId: v.id("customerComments"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the comment
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Kommentar ikke funnet");
    }

    // Verify the project belongs to the user
    const project = await ctx.db.get(comment.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å slette kommentarer for dette prosjektet");
    }

    // Delete the comment
    await ctx.db.delete(args.commentId);

    return { success: true };
  }
});

// ============================================================================
// NEW THREADED COMMENT SYSTEM
// ============================================================================

// Add a new customer comment (creates a new thread)
export const addThreadedComment = mutation({
  args: {
    projectId: v.id("projects"),
    sharedId: v.string(),
    customerName: v.string(),
    customerEmail: v.optional(v.string()),
    message: v.string(),
    ipAddress: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (!project.isPubliclyShared) {
      throw new Error("Dette prosjektet er ikke delt offentlig");
    }

    // Check if sharing has expired
    if (project.shareSettings?.expiresAt && project.shareSettings.expiresAt < Date.now()) {
      throw new Error("Delingslinken har utløpt");
    }

    // Check if comments are allowed
    if (!project.shareSettings?.allowCustomerComments) {
      throw new Error("Kommentarer er ikke tillatt for dette prosjektet");
    }

    // Validate input
    if (!args.customerName.trim()) {
      throw new Error("Navn er påkrevd");
    }

    if (!args.message.trim()) {
      throw new Error("Kommentar kan ikke være tom");
    }

    if (args.message.length > 1000) {
      throw new Error("Kommentar kan ikke være lengre enn 1000 tegn");
    }

    // Create new thread
    const threadId = nanoid(12);

    // Add the root comment
    const commentId = await ctx.db.insert("customerComments", {
      projectId: args.projectId,
      sharedId: args.sharedId,
      threadId,
      parentCommentId: undefined,
      isRootComment: true,
      authorType: "customer",
      customerName: args.customerName.trim(),
      customerEmail: args.customerEmail?.trim(),
      contractorId: undefined,
      message: args.message.trim(),
      createdAt: Date.now(),
      ipAddress: args.ipAddress,
      isReadByContractor: false, // New customer comments are unread by default
      contractorReadAt: undefined,
      isReadByCustomer: true, // Customer automatically reads their own comments
      customerReadAt: Date.now(),
      readAt: undefined // Legacy field
    });

    return { success: true, commentId, threadId };
  }
});

// Add a customer reply to an existing thread
export const addCustomerReply = mutation({
  args: {
    projectId: v.id("projects"),
    sharedId: v.string(),
    parentCommentId: v.id("customerComments"),
    customerName: v.string(),
    customerEmail: v.optional(v.string()),
    message: v.string(),
    ipAddress: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Get the parent comment to validate thread
    const parentComment = await ctx.db.get(args.parentCommentId);
    if (!parentComment) {
      throw new Error("Opprinnelig kommentar ikke funnet");
    }

    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (!project.isPubliclyShared) {
      throw new Error("Dette prosjektet er ikke delt offentlig");
    }

    // Check if sharing has expired
    if (project.shareSettings?.expiresAt && project.shareSettings.expiresAt < Date.now()) {
      throw new Error("Delingslinken har utløpt");
    }

    // Check if comments are allowed
    if (!project.shareSettings?.allowCustomerComments) {
      throw new Error("Kommentarer er ikke tillatt for dette prosjektet");
    }

    // Validate input
    if (!args.customerName.trim()) {
      throw new Error("Navn er påkrevd");
    }

    if (!args.message.trim()) {
      throw new Error("Svar kan ikke være tomt");
    }

    if (args.message.length > 1000) {
      throw new Error("Svar kan ikke være lengre enn 1000 tegn");
    }

    // Add the reply to the same thread
    const commentId = await ctx.db.insert("customerComments", {
      projectId: args.projectId,
      sharedId: args.sharedId,
      threadId: parentComment.threadId,
      parentCommentId: args.parentCommentId,
      isRootComment: false,
      authorType: "customer",
      customerName: args.customerName.trim(),
      customerEmail: args.customerEmail?.trim(),
      contractorId: undefined,
      message: args.message.trim(),
      createdAt: Date.now(),
      ipAddress: args.ipAddress,
      isReadByContractor: false, // New customer replies are unread by default
      contractorReadAt: undefined,
      isReadByCustomer: true, // Customer automatically reads their own replies
      customerReadAt: Date.now(),
      readAt: undefined // Legacy field
    });

    return { success: true, commentId };
  }
});

// Add a contractor reply to an existing thread
export const addContractorReply = mutation({
  args: {
    parentCommentId: v.id("customerComments"),
    userId: v.string(),
    message: v.string()
  },
  handler: async (ctx, args) => {
    // Get the parent comment to validate thread
    const parentComment = await ctx.db.get(args.parentCommentId);
    if (!parentComment) {
      throw new Error("Opprinnelig kommentar ikke funnet");
    }

    // Verify the project belongs to the user
    const project = await ctx.db.get(parentComment.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å svare på kommentarer for dette prosjektet");
    }

    // Validate message
    if (!args.message.trim()) {
      throw new Error("Svar kan ikke være tomt");
    }

    if (args.message.length > 1000) {
      throw new Error("Svar kan ikke være lengre enn 1000 tegn");
    }

    // Add the contractor reply to the same thread
    const commentId = await ctx.db.insert("customerComments", {
      projectId: parentComment.projectId,
      sharedId: parentComment.sharedId,
      threadId: parentComment.threadId,
      parentCommentId: args.parentCommentId,
      isRootComment: false,
      authorType: "contractor",
      customerName: undefined,
      customerEmail: undefined,
      contractorId: args.userId,
      message: args.message.trim(),
      createdAt: Date.now(),
      ipAddress: undefined,
      isReadByContractor: true, // Contractor replies are automatically read
      contractorReadAt: Date.now(),
      isReadByCustomer: false, // New contractor replies are unread by customers
      customerReadAt: undefined,
      readAt: Date.now() // Legacy field
    });

    return { success: true, commentId };
  }
});

// Get threaded comments for a project (for contractors)
export const getThreadedByProject = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project belongs to the user
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til kommentarer for dette prosjektet");
    }

    // Get all comments for the project
    const allComments = await ctx.db
      .query("customerComments")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .order("asc")
      .collect();

    // Filter for new threaded comments (those with threadId)
    const threadedComments = allComments.filter(comment => comment.threadId && comment.authorType);

    // Group comments by thread
    const threads: Record<string, any[]> = {};

    threadedComments.forEach(comment => {
      if (comment.threadId && !threads[comment.threadId]) {
        threads[comment.threadId] = [];
      }
      if (comment.threadId) {
        threads[comment.threadId].push(comment);
      }
    });

    // Sort each thread by creation time and structure the response
    const structuredThreads = Object.values(threads).map(thread => {
      const sortedThread = thread.sort((a, b) => a.createdAt - b.createdAt);
      const rootComment = sortedThread.find(c => c.isRootComment === true);
      const replies = sortedThread.filter(c => c.isRootComment === false);

      return {
        threadId: thread[0].threadId!,
        rootComment,
        replies,
        totalReplies: replies.length,
        lastActivity: Math.max(...sortedThread.map(c => c.createdAt))
      };
    });

    // Sort threads by last activity (most recent first)
    structuredThreads.sort((a, b) => b.lastActivity - a.lastActivity);

    return structuredThreads;
  }
});

// Check if a project has any legacy comments (for conditional UI display)
export const hasLegacyComments = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    // Get all comments for the project
    const comments = await ctx.db
      .query("customerComments")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();

    // Check if any comments are legacy (have contractorReply or comment field without threadId)
    const hasLegacy = comments.some(comment =>
      comment.contractorReply !== undefined ||
      (comment.comment !== undefined && comment.threadId === undefined)
    );

    return hasLegacy;
  }
});

// Get threaded comments for public view (shared projects)
export const getThreadedForPublicView = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return [];
    }

    if (!project.isPubliclyShared) {
      return [];
    }

    // Check if sharing has expired
    if (project.shareSettings?.expiresAt && project.shareSettings.expiresAt < Date.now()) {
      return [];
    }

    // Get all comments for the project
    const allComments = await ctx.db
      .query("customerComments")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .order("asc")
      .collect();

    // Filter for new threaded comments (those with threadId)
    const threadedComments = allComments.filter(comment => comment.threadId && comment.authorType);

    // Group comments by thread
    const threads: Record<string, any[]> = {};

    threadedComments.forEach(comment => {
      if (comment.threadId && !threads[comment.threadId]) {
        threads[comment.threadId] = [];
      }
      if (comment.threadId) {
        threads[comment.threadId].push(comment);
      }
    });

    // Sort each thread by creation time and structure the response
    const structuredThreads = Object.values(threads).map(thread => {
      const sortedThread = thread.sort((a, b) => a.createdAt - b.createdAt);
      const rootComment = sortedThread.find(c => c.isRootComment === true);
      const replies = sortedThread.filter(c => c.isRootComment === false);

      return {
        threadId: thread[0].threadId!,
        rootComment,
        replies,
        totalReplies: replies.length,
        lastActivity: Math.max(...sortedThread.map(c => c.createdAt))
      };
    });

    // Sort threads by last activity (most recent first)
    structuredThreads.sort((a, b) => b.lastActivity - a.lastActivity);

    return structuredThreads;
  }
});

// Delete a threaded comment or reply
export const deleteThreadedComment = mutation({
  args: {
    commentId: v.id("customerComments"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the comment
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Kommentar ikke funnet");
    }

    // Verify the project belongs to the user
    const project = await ctx.db.get(comment.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å slette kommentarer for dette prosjektet");
    }

    // If this is a root comment, delete all replies in the thread
    if (comment.isRootComment && comment.threadId) {
      const threadComments = await ctx.db
        .query("customerComments")
        .withIndex("by_thread", (q) => q.eq("threadId", comment.threadId))
        .collect();

      // Delete all comments in the thread
      for (const threadComment of threadComments) {
        await ctx.db.delete(threadComment._id);
      }
    } else {
      // Just delete the individual reply
      await ctx.db.delete(args.commentId);
    }

    return { success: true };
  }
});

// ============================================================================
// UNREAD COMMENTS MANAGEMENT
// ============================================================================

// Get count of unread customer comments for a contractor
export const getUnreadCount = query({
  args: {
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get all projects for this user
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    if (projects.length === 0) {
      return 0;
    }

    const projectIds = projects.map(p => p._id);

    // Count unread customer comments across all user's projects
    let unreadCount = 0;

    for (const projectId of projectIds) {
      const unreadComments = await ctx.db
        .query("customerComments")
        .withIndex("by_project_unread", (q) =>
          q.eq("projectId", projectId)
           .eq("isReadByContractor", false)
           .eq("authorType", "customer")
        )
        .collect();

      unreadCount += unreadComments.length;
    }

    return unreadCount;
  }
});

// Get detailed list of unread customer comments for a contractor
export const getUnreadComments = query({
  args: {
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get all projects for this user
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    if (projects.length === 0) {
      return [];
    }

    const projectIds = projects.map(p => p._id);
    const unreadComments = [];

    // Get unread customer comments from all projects
    for (const project of projects) {
      const comments = await ctx.db
        .query("customerComments")
        .withIndex("by_project_unread", (q) =>
          q.eq("projectId", project._id)
           .eq("isReadByContractor", false)
           .eq("authorType", "customer")
        )
        .order("desc") // Most recent first
        .collect();

      // Add project context to each comment
      for (const comment of comments) {
        unreadComments.push({
          ...comment,
          project: {
            _id: project._id,
            name: project.name,
            description: project.description
          }
        });
      }
    }

    // Sort all comments by creation date (most recent first)
    unreadComments.sort((a, b) => b.createdAt - a.createdAt);

    return unreadComments;
  }
});

// Get all recent customer comments for a contractor (including read ones for smart filtering)
export const getRecentCustomerComments = query({
  args: {
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get all projects for this user
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    if (projects.length === 0) {
      return [];
    }

    const allComments = [];

    // Get all customer comments from all projects (both read and unread)
    for (const project of projects) {
      const comments = await ctx.db
        .query("customerComments")
        .withIndex("by_project", (q) => q.eq("projectId", project._id))
        .filter((q) => q.eq(q.field("authorType"), "customer"))
        .order("desc") // Most recent first
        .collect();

      // Add project context to each comment
      for (const comment of comments) {
        allComments.push({
          ...comment,
          project: {
            _id: project._id,
            name: project.name,
            description: project.description
          }
        });
      }
    }

    // Sort all comments by creation date (most recent first)
    allComments.sort((a, b) => b.createdAt - a.createdAt);

    return allComments;
  }
});

// Mark a comment as read by contractor
export const markAsRead = mutation({
  args: {
    commentId: v.id("customerComments"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the comment
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Kommentar ikke funnet");
    }

    // Verify the user owns the project
    const project = await ctx.db.get(comment.projectId);
    if (!project || project.userId !== args.userId) {
      throw new Error("Ikke autorisert til å markere denne kommentaren som lest");
    }

    // Only mark customer comments as read
    if (comment.authorType !== "customer") {
      throw new Error("Kan kun markere kundekommentarer som lest");
    }

    // Update the comment
    const now = Date.now();
    await ctx.db.patch(args.commentId, {
      isReadByContractor: true,
      contractorReadAt: now,
      readAt: now // Update legacy field for backward compatibility
    });

    return { success: true };
  }
});

// Mark all unread comments as read for a contractor
export const markAllAsRead = mutation({
  args: {
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get all projects for this user
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    if (projects.length === 0) {
      return { success: true, markedCount: 0 };
    }

    let markedCount = 0;
    const now = Date.now();

    // Mark all unread customer comments as read
    for (const project of projects) {
      const unreadComments = await ctx.db
        .query("customerComments")
        .withIndex("by_project_unread", (q) =>
          q.eq("projectId", project._id)
           .eq("isReadByContractor", false)
           .eq("authorType", "customer")
        )
        .collect();

      for (const comment of unreadComments) {
        await ctx.db.patch(comment._id, {
          isReadByContractor: true,
          contractorReadAt: now,
          readAt: now // Update legacy field for backward compatibility
        });
        markedCount++;
      }
    }

    return { success: true, markedCount };
  }
});

// ============================================================================
// AUTOMATIC READ DETECTION SYSTEM
// ============================================================================

// Automatically mark comments as read when viewed by contractor
export const autoMarkAsReadByContractor = mutation({
  args: {
    commentIds: v.array(v.id("customerComments")),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    if (args.commentIds.length === 0) {
      return { success: true, markedCount: 0 };
    }

    let markedCount = 0;
    const now = Date.now();

    for (const commentId of args.commentIds) {
      const comment = await ctx.db.get(commentId);
      if (!comment) continue;

      // Verify the user owns the project
      const project = await ctx.db.get(comment.projectId);
      if (!project || project.userId !== args.userId) continue;

      // Only mark customer comments as read by contractor
      if (comment.authorType === "customer" && !comment.isReadByContractor) {
        await ctx.db.patch(commentId, {
          isReadByContractor: true,
          contractorReadAt: now,
          readAt: now // Update legacy field for backward compatibility
        });
        markedCount++;
      }
    }

    return { success: true, markedCount };
  }
});

// Automatically mark comments as read when viewed by customer on shared project
export const autoMarkAsReadByCustomer = mutation({
  args: {
    commentIds: v.array(v.id("customerComments")),
    sharedId: v.string()
  },
  handler: async (ctx, args) => {
    if (args.commentIds.length === 0) {
      return { success: true, markedCount: 0 };
    }

    let markedCount = 0;
    const now = Date.now();

    for (const commentId of args.commentIds) {
      const comment = await ctx.db.get(commentId);
      if (!comment) continue;

      // Verify the comment belongs to the shared project
      if (comment.sharedId !== args.sharedId) continue;

      // Verify the project is publicly shared
      const project = await ctx.db.get(comment.projectId);
      if (!project || !project.isPubliclyShared) continue;

      // Check if sharing has expired
      if (project.shareSettings?.expiresAt && project.shareSettings.expiresAt < Date.now()) continue;

      // Only mark contractor comments as read by customer
      if (comment.authorType === "contractor" && !comment.isReadByCustomer) {
        await ctx.db.patch(commentId, {
          isReadByCustomer: true,
          customerReadAt: now
        });
        markedCount++;
      }
    }

    return { success: true, markedCount };
  }
});

// Get read status for comments (for displaying read indicators)
export const getReadStatus = query({
  args: {
    commentIds: v.array(v.id("customerComments")),
    viewerType: v.union(v.literal("contractor"), v.literal("customer")),
    userId: v.optional(v.string()),
    sharedId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const readStatuses: Record<string, {
      isReadByContractor: boolean;
      contractorReadAt?: number;
      isReadByCustomer: boolean;
      customerReadAt?: number;
      authorType: "customer" | "contractor";
    }> = {};

    for (const commentId of args.commentIds) {
      const comment = await ctx.db.get(commentId);
      if (!comment) continue;

      // Authorization check
      if (args.viewerType === "contractor") {
        const project = await ctx.db.get(comment.projectId);
        if (!project || project.userId !== args.userId) continue;
      } else if (args.viewerType === "customer") {
        if (comment.sharedId !== args.sharedId) continue;
        const project = await ctx.db.get(comment.projectId);
        if (!project || !project.isPubliclyShared) continue;
      }

      readStatuses[commentId] = {
        isReadByContractor: comment.isReadByContractor || false,
        contractorReadAt: comment.contractorReadAt || comment.readAt, // Fallback to legacy field
        isReadByCustomer: comment.isReadByCustomer || false,
        customerReadAt: comment.customerReadAt,
        authorType: comment.authorType || "customer"
      };
    }

    return readStatuses;
  }
});
