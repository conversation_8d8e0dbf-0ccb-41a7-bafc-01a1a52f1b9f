import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Add a customer comment to a shared project
export const addComment = mutation({
  args: {
    projectId: v.id("projects"),
    sharedId: v.string(),
    customerName: v.string(),
    customerEmail: v.optional(v.string()),
    comment: v.string(),
    ipAddress: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (!project.isPubliclyShared) {
      throw new Error("Dette prosjektet er ikke delt offentlig");
    }

    // Check if sharing has expired
    if (project.shareSettings?.expiresAt && project.shareSettings.expiresAt < Date.now()) {
      throw new Error("Delingslinken har utløpt");
    }

    // Check if comments are allowed
    if (!project.shareSettings?.allowCustomerComments) {
      throw new Error("Kommentarer er ikke tillatt for dette prosjektet");
    }

    // Validate input
    if (!args.customerName.trim()) {
      throw new Error("Navn er påkrevd");
    }

    if (!args.comment.trim()) {
      throw new Error("Kommentar kan ikke være tom");
    }

    if (args.comment.length > 1000) {
      throw new Error("Kommentar kan ikke være lengre enn 1000 tegn");
    }

    // Add the comment (published immediately)
    const commentId = await ctx.db.insert("customerComments", {
      projectId: args.projectId,
      sharedId: args.sharedId,
      customerName: args.customerName.trim(),
      customerEmail: args.customerEmail?.trim(),
      comment: args.comment.trim(),
      createdAt: Date.now(),
      ipAddress: args.ipAddress
    });

    return { success: true, commentId };
  }
});

// Get comments for a project (for contractors)
export const getByProject = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project belongs to the user
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til kommentarer for dette prosjektet");
    }

    // Get all comments for the project
    const comments = await ctx.db
      .query("customerComments")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .order("desc")
      .collect();

    return comments;
  }
});

// Get all comments for a project (for public view)
export const getForPublicView = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return [];
    }

    if (!project.isPubliclyShared) {
      return [];
    }

    // Check if sharing has expired
    if (project.shareSettings?.expiresAt && project.shareSettings.expiresAt < Date.now()) {
      return [];
    }

    // Get all comments (no approval needed)
    const comments = await ctx.db
      .query("customerComments")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .order("desc")
      .collect();

    return comments;
  }
});

// Add a contractor reply to a customer comment
export const addReply = mutation({
  args: {
    commentId: v.id("customerComments"),
    userId: v.string(),
    replyMessage: v.string()
  },
  handler: async (ctx, args) => {
    // Get the comment
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Kommentar ikke funnet");
    }

    // Verify the project belongs to the user
    const project = await ctx.db.get(comment.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å svare på kommentarer for dette prosjektet");
    }

    // Validate reply message
    if (!args.replyMessage.trim()) {
      throw new Error("Svar kan ikke være tomt");
    }

    if (args.replyMessage.length > 1000) {
      throw new Error("Svar kan ikke være lengre enn 1000 tegn");
    }

    // Add the reply
    await ctx.db.patch(args.commentId, {
      contractorReply: {
        message: args.replyMessage.trim(),
        repliedAt: Date.now(),
        repliedBy: args.userId
      }
    });

    return { success: true };
  }
});

// Remove a contractor reply from a comment
export const removeReply = mutation({
  args: {
    commentId: v.id("customerComments"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the comment
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Kommentar ikke funnet");
    }

    // Verify the project belongs to the user
    const project = await ctx.db.get(comment.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å fjerne svar for dette prosjektet");
    }

    // Remove the reply
    await ctx.db.patch(args.commentId, {
      contractorReply: undefined
    });

    return { success: true };
  }
});

// Delete a customer comment
export const deleteComment = mutation({
  args: {
    commentId: v.id("customerComments"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get the comment
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Kommentar ikke funnet");
    }

    // Verify the project belongs to the user
    const project = await ctx.db.get(comment.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å slette kommentarer for dette prosjektet");
    }

    // Delete the comment
    await ctx.db.delete(args.commentId);

    return { success: true };
  }
});
