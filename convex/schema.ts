import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
  // Customer data table for AI-agent friendly structure
  customers: defineTable({
    name: v.string(),                    // Customer name (required) - shown in UI and search
    type: v.union(v.literal("privat"), v.literal("firma")), // Customer type: "privat" or "firma" - controls logic and display
    contactPerson: v.optional(v.string()), // Contact person (if type === "firma") - e.g. "<PERSON><PERSON>"
    phone: v.optional(v.string()),       // Phone number (optional) - for quick contact
    email: v.optional(v.string()),       // Email address (optional) - used in reporting or notifications
    address: v.string(),                 // Project address (required) - where work is performed
    orgNumber: v.optional(v.string()),   // Organization number (optional, only for firma) - legal ID
    notes: v.optional(v.string()),       // Notes (optional) - free text: key code, "customer is allergic to dogs", etc.
    userId: v.string(),                  // Owner of this customer record
    createdAt: v.number()                // Creation timestamp
  })
    .index("by_user", ["userId"])
    .index("by_type", ["type"])
    .index("by_user_and_type", ["userId", "type"])
    .index("by_org_number", ["orgNumber"]),

  projects: defineTable({
    name: v.string(),
    description: v.string(),
    userId: v.string(),
    customerId: v.optional(v.id("customers")), // Reference to customer - enables AI queries like "projects for customer X"
    sharedId: v.string(),
    createdAt: v.number(),
    // Job information for contractor workflow documentation
    jobData: v.optional(v.object({
      jobDescription: v.string(),           // "Hva skal gjøres?" - detailed job description
      photos: v.array(v.object({           // "Bilder fra befaring" - site inspection photos
        url: v.string(),                   // Image URL from Convex storage
        note: v.optional(v.string()),      // Optional comment/note for the image
        capturedAt: v.optional(v.number()) // Timestamp when photo was taken
      })),
      accessNotes: v.string(),             // "Tilkomst og forhold" - access and site conditions
      equipmentNeeds: v.string(),          // "Hva må medbringes?" - equipment and materials needed
      unresolvedQuestions: v.string(),     // "Hva må avklares?" - questions that need clarification
      personalNotes: v.string()            // "Egne notater" - contractor's personal notes
    }))
  })
    .index("by_user", ["userId"])
    .index("by_shared_id", ["sharedId"])
    .index("by_customer", ["customerId"])
    .index("by_user_and_customer", ["userId", "customerId"]),

  logEntries: defineTable({
    projectId: v.id("projects"),
    userId: v.string(),
    description: v.string(),
    imageId: v.optional(v.id("_storage")),
    createdAt: v.number(),
    // Edit history and tracking fields
    isEdited: v.optional(v.boolean()),
    lastEditedAt: v.optional(v.number()),
    editHistory: v.optional(v.array(v.object({
      version: v.number(),
      editedAt: v.number(),
      description: v.string(),
      imageId: v.optional(v.id("_storage")),
      changeType: v.string(), // "description", "image", "both"
      changeSummary: v.string()
    })))
  })
    .index("by_project", ["projectId"])
    .index("by_user", ["userId"])
    .index("by_project_and_user", ["projectId", "userId"])
});
