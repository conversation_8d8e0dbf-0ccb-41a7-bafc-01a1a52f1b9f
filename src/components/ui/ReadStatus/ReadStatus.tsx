import React from 'react';
import { TextMuted } from '../Text/TextMuted';

interface ReadStatusProps {
  /** Whether the comment is read */
  isRead: boolean;
  /** Timestamp when the comment was read */
  readAt?: number;
  /** Type of reader (contractor or customer) */
  readerType: 'contractor' | 'customer';
  /** Size variant */
  size?: 'sm' | 'md';
  /** Show full timestamp or just read indicator */
  showTimestamp?: boolean;
  /** Custom className */
  className?: string;
}

export const ReadStatus: React.FC<ReadStatusProps> = ({
  isRead,
  readAt,
  readerType,
  size = 'md',
  showTimestamp = true,
  className = ''
}) => {
  const formatReadTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    // If less than 24 hours ago, show relative time
    if (diffInHours < 24) {
      if (diffInHours < 1) {
        const diffInMinutes = Math.floor(diffInHours * 60);
        return diffInMinutes <= 1 ? 'Akkurat nå' : `${diffInMinutes} min siden`;
      }
      const hours = Math.floor(diffInHours);
      return `${hours} ${hours === 1 ? 'time' : 'timer'} siden`;
    }

    // Otherwise show date and time
    return date.toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getReaderLabel = () => {
    return readerType === 'contractor' ? 'kontraktør' : 'kunde';
  };

  const iconSize = size === 'sm' ? 'w-3 h-3' : 'w-4 h-4';
  const textSize = size === 'sm' ? 'text-xs' : 'text-sm';

  if (!isRead) {
    return (
      <div className={`flex items-center gap-1 ${className}`}>
        <div className={`${iconSize} rounded-full bg-jobblogg-accent flex-shrink-0`} 
             title={`Ulest av ${getReaderLabel()}`}
             role="img"
             aria-label={`Ulest av ${getReaderLabel()}`}>
          <div className="w-full h-full rounded-full bg-jobblogg-accent animate-pulse" />
        </div>
        <TextMuted className={`${textSize} font-medium`}>
          Ulest
        </TextMuted>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <svg 
        className={`${iconSize} text-jobblogg-success flex-shrink-0`}
        fill="currentColor" 
        viewBox="0 0 20 20"
        title={`Lest av ${getReaderLabel()}${readAt ? ` ${formatReadTime(readAt)}` : ''}`}
        role="img"
        aria-label={`Lest av ${getReaderLabel()}${readAt ? ` ${formatReadTime(readAt)}` : ''}`}
      >
        <path 
          fillRule="evenodd" 
          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
          clipRule="evenodd" 
        />
      </svg>
      {showTimestamp && readAt && (
        <TextMuted className={`${textSize}`}>
          Lest {formatReadTime(readAt)}
        </TextMuted>
      )}
      {showTimestamp && !readAt && (
        <TextMuted className={`${textSize}`}>
          Lest
        </TextMuted>
      )}
    </div>
  );
};
