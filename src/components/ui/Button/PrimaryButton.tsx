import React from 'react';

interface PrimaryButtonProps {
  /** Button content */
  children: React.ReactNode;
  /** Click handler */
  onClick?: () => void;
  /** Button type for forms */
  type?: 'button' | 'submit' | 'reset';
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  loading?: boolean;
  /** Icon to display before text */
  icon?: React.ReactNode;
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success';
  /** Button size */
  size?: 'sm' | 'md' | 'lg';
  /** Full width button */
  fullWidth?: boolean;
}

/**
 * Primary button component following JobbLogg design system
 * 
 * @example
 * ```tsx
 * <PrimaryButton onClick={() => console.log('clicked')}>
 *   Opprett prosjekt
 * </PrimaryButton>
 * 
 * <PrimaryButton type="submit" loading={isLoading} icon={<PlusIcon />}>
 *   Lagre endringer
 * </PrimaryButton>
 * ```
 */
export const PrimaryButton: React.FC<PrimaryButtonProps> = ({
  children,
  onClick,
  type = 'button',
  disabled = false,
  className = '',
  loading = false,
  icon,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
}) => {
  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if ((event.key === 'Enter' || event.key === ' ') && !disabled && !loading) {
      event.preventDefault();
      if (onClick) onClick();
    }
  };

  // Get variant styles - Updated for modern flat design
  const getVariantStyles = () => {
    switch (variant) {
      case 'secondary':
        return 'btn-secondary-solid';
      case 'outline':
        return 'btn-outline';
      case 'ghost':
        return 'btn-ghost-enhanced';
      case 'danger':
        return 'btn-error-soft';
      default:
        return 'btn-primary-solid';
    }
  };

  // Get size styles with mobile-optimized touch targets
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-2 text-sm touch-target'; // Minimum 44px height
      case 'lg':
        return 'px-6 py-3 text-lg touch-target-large'; // 48px height for large buttons
      default:
        return 'px-4 py-2.5 text-base touch-target'; // Standard 44px touch target
    }
  };

  return (
    <button
      type={type}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled || loading}
      className={`
        ${getVariantStyles()}
        ${getSizeStyles()}
        ${fullWidth ? 'w-full' : ''}
        focus-ring-enhanced interactive-press touch-feedback group
        ${loading ? 'animate-pulse-soft' : ''}
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      aria-disabled={disabled || loading}
    >
      {loading ? (
        <div className="flex items-center gap-2 animate-fade-in">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          <span className="animate-pulse-soft">Laster...</span>
        </div>
      ) : (
        <div className="flex items-center gap-2 animate-fade-in">
          {icon && <span className="flex-shrink-0 transition-transform duration-200 group-hover:scale-110">{icon}</span>}
          <span>{children}</span>
        </div>
      )}
    </button>
  );
};

export default PrimaryButton;
