import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { PWAInstallBanner } from '../PWAInstallBanner';

// Mock the usePWA hook
jest.mock('../../hooks/usePWA', () => ({
  usePWA: () => ({
    isInstallable: true,
    isInstalled: false,
    installApp: jest.fn().mockResolvedValue(true)
  }),
  PWAUtils: {
    getInstallInstructions: () => 'Test instructions'
  }
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('PWAInstallBanner', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should not render when previously dismissed', () => {
    // Mock localStorage to return that banner was dismissed
    localStorageMock.getItem.mockReturnValue('true');
    
    render(<PWAInstallBanner />);
    
    // Banner should not be visible
    expect(screen.queryByText('Installer JobbLogg')).not.toBeInTheDocument();
  });

  it('should render when not previously dismissed', () => {
    // Mock localStorage to return null (not dismissed)
    localStorageMock.getItem.mockReturnValue(null);
    
    render(<PWAInstallBanner />);
    
    // Banner should be visible
    expect(screen.getByText('Installer JobbLogg')).toBeInTheDocument();
    expect(screen.getByText('Ikke nå')).toBeInTheDocument();
  });

  it('should save dismissal state when "Ikke nå" is clicked', () => {
    localStorageMock.getItem.mockReturnValue(null);
    
    render(<PWAInstallBanner />);
    
    // Click "Ikke nå" button
    fireEvent.click(screen.getByText('Ikke nå'));
    
    // Should save dismissal state to localStorage
    expect(localStorageMock.setItem).toHaveBeenCalledWith('pwa-install-dismissed', 'true');
  });

  it('should save dismissal state when close button is clicked', () => {
    localStorageMock.getItem.mockReturnValue(null);
    
    render(<PWAInstallBanner />);
    
    // Click close button (X)
    const closeButton = screen.getByLabelText('Lukk installasjonsbanner');
    fireEvent.click(closeButton);
    
    // Should save dismissal state to localStorage
    expect(localStorageMock.setItem).toHaveBeenCalledWith('pwa-install-dismissed', 'true');
  });

  it('should handle localStorage errors gracefully', () => {
    // Mock localStorage to throw an error
    localStorageMock.getItem.mockImplementation(() => {
      throw new Error('localStorage not available');
    });
    
    // Should not crash and should default to showing the banner
    render(<PWAInstallBanner />);
    
    expect(screen.getByText('Installer JobbLogg')).toBeInTheDocument();
  });
});
