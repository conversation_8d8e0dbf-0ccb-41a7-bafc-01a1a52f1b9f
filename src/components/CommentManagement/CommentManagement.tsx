import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  Heading3,
  TextMedium,
  TextMuted,
  TextStrong,
  PrimaryButton,
  SecondaryButton,
  ConfirmDialog,
  TextArea
} from '../ui';

interface CommentManagementProps {
  projectId: string;
}

export const CommentManagement: React.FC<CommentManagementProps> = ({ projectId }) => {
  const { user } = useUser();
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  const [confirmDelete, setConfirmDelete] = useState<{
    commentId: string;
    customerName: string;
  } | null>(null);

  // Fetch all comments for this project
  const comments = useQuery(
    api.customerComments.getByProject,
    user ? { projectId: projectId as any, userId: user.id } : "skip"
  );

  // Mutations
  const addReply = useMutation(api.customerComments.addReply);
  const removeReply = useMutation(api.customerComments.removeReply);
  const deleteComment = useMutation(api.customerComments.deleteComment);

  const handleAddReply = async (commentId: string) => {
    if (!user || !replyText.trim()) return;

    try {
      await addReply({
        commentId: commentId as any,
        userId: user.id,
        replyMessage: replyText.trim()
      });
      setReplyingTo(null);
      setReplyText('');
    } catch (error) {
      console.error('Failed to add reply:', error);
    }
  };

  const handleRemoveReply = async (commentId: string) => {
    if (!user) return;

    try {
      await removeReply({
        commentId: commentId as any,
        userId: user.id
      });
    } catch (error) {
      console.error('Failed to remove reply:', error);
    }
  };

  const handleDelete = async (commentId: string) => {
    if (!user) return;

    try {
      await deleteComment({
        commentId: commentId as any,
        userId: user.id
      });
      setConfirmDelete(null);
    } catch (error) {
      console.error('Failed to delete comment:', error);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!comments) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-6 bg-jobblogg-neutral rounded w-1/3"></div>
        <div className="space-y-3">
          {[1, 2].map((i) => (
            <div key={i} className="h-24 bg-jobblogg-neutral rounded-xl"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Heading3>Kundekommentarer</Heading3>
        {comments.length > 0 && (
          <div className="bg-jobblogg-primary-soft text-jobblogg-primary px-3 py-1 rounded-full text-sm font-medium">
            {comments.length} kommentar{comments.length !== 1 ? 'er' : ''}
          </div>
        )}
      </div>

      {comments.length === 0 ? (
        <div className="text-center py-8">
          <TextMuted>Ingen kundekommentarer ennå</TextMuted>
        </div>
      ) : (
        <div className="space-y-4">
          {comments.map((comment) => {
            const isReplying = replyingTo === comment._id;

            return (
              <div key={comment._id} className="bg-white border border-jobblogg-border rounded-xl p-6">
              {/* Customer Comment */}
              <div className="mb-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                      <div>
                        <TextStrong>{comment.customerName}</TextStrong>
                        <div className="text-xs text-jobblogg-text-muted">Kunde</div>
                      </div>
                    </div>
                    {comment.customerEmail && (
                      <TextMuted className="text-sm ml-10">{comment.customerEmail}</TextMuted>
                    )}
                  </div>
                  <TextMuted className="text-sm">
                    {formatDate(comment.createdAt)}
                  </TextMuted>
                </div>

                <div className="ml-10">
                  <TextMedium className="mb-4">{comment.comment}</TextMedium>
                </div>
              </div>

              {/* Contractor Reply */}
              {comment.contractorReply ? (
                <div className="ml-6 pl-4 border-l-2 border-jobblogg-accent bg-jobblogg-accent-soft rounded-r-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-jobblogg-accent rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H5m14 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2z" />
                        </svg>
                      </div>
                      <div>
                        <TextStrong className="text-jobblogg-accent">Kontraktør</TextStrong>
                        <div className="text-xs text-jobblogg-text-muted">Svar</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <TextMuted className="text-sm">
                        {formatDate(comment.contractorReply.repliedAt)}
                      </TextMuted>
                      <SecondaryButton
                        size="sm"
                        variant="danger"
                        onClick={() => handleRemoveReply(comment._id)}
                      >
                        Fjern svar
                      </SecondaryButton>
                    </div>
                  </div>
                  <TextMedium>{comment.contractorReply.message}</TextMedium>
                </div>
              ) : isReplying ? (
                <div className="ml-6 pl-4 border-l-2 border-jobblogg-accent bg-jobblogg-accent-soft rounded-r-lg p-4">
                  <div className="space-y-4">
                    <TextStrong className="text-jobblogg-accent">Skriv svar som kontraktør:</TextStrong>
                    <TextArea
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      placeholder="Skriv ditt svar til kunden..."
                      rows={3}
                      maxLength={1000}
                    />
                    <div className="flex items-center gap-3">
                      <PrimaryButton
                        size="sm"
                        onClick={() => handleAddReply(comment._id)}
                        disabled={!replyText.trim()}
                      >
                        Send svar
                      </PrimaryButton>
                      <SecondaryButton
                        size="sm"
                        onClick={() => {
                          setReplyingTo(null);
                          setReplyText('');
                        }}
                      >
                        Avbryt
                      </SecondaryButton>
                    </div>
                  </div>
                </div>
              ) : null}

              {/* Action Buttons */}
              <div className="flex items-center justify-between mt-4 pt-4 border-t border-jobblogg-border">
                <div className="flex items-center gap-3">
                  {!comment.contractorReply && !isReplying && (
                    <PrimaryButton
                      size="sm"
                      onClick={() => setReplyingTo(comment._id)}
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                      </svg>
                      Svar
                    </PrimaryButton>
                  )}
                </div>
                <SecondaryButton
                  size="sm"
                  variant="danger"
                  onClick={() => setConfirmDelete({
                    commentId: comment._id,
                    customerName: comment.customerName
                  })}
                >
                  Slett kommentar
                </SecondaryButton>
              </div>
            </div>
            );
          })}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      {confirmDelete && (
        <ConfirmDialog
          isOpen={true}
          onClose={() => setConfirmDelete(null)}
          onConfirm={() => handleDelete(confirmDelete.commentId)}
          title="Slett kommentar"
          message={`Er du sikker på at du vil slette kommentaren fra ${confirmDelete.customerName}? Dette vil også slette eventuelle svar. Denne handlingen kan ikke angres.`}
          confirmText="Slett"
          variant="danger"
        />
      )}
    </div>
  );
};
