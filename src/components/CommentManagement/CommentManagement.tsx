import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { 
  Heading3, 
  TextMedium, 
  TextMuted, 
  TextStrong, 
  PrimaryButton, 
  SecondaryButton,
  ConfirmDialog 
} from '../ui';

interface CommentManagementProps {
  projectId: string;
}

export const CommentManagement: React.FC<CommentManagementProps> = ({ projectId }) => {
  const { user } = useUser();
  const [confirmAction, setConfirmAction] = useState<{
    type: 'approve' | 'reject' | 'delete';
    commentId: string;
    customerName: string;
  } | null>(null);

  // Fetch all comments for this project
  const comments = useQuery(
    api.customerComments.getByProject,
    user ? { projectId: projectId as any, userId: user.id } : "skip"
  );

  // Mutations
  const moderateComment = useMutation(api.customerComments.moderateComment);
  const deleteComment = useMutation(api.customerComments.deleteComment);

  const handleApprove = async (commentId: string) => {
    if (!user) return;
    
    try {
      await moderateComment({
        commentId: commentId as any,
        userId: user.id,
        isApproved: true
      });
      setConfirmAction(null);
    } catch (error) {
      console.error('Failed to approve comment:', error);
    }
  };

  const handleReject = async (commentId: string) => {
    if (!user) return;
    
    try {
      await moderateComment({
        commentId: commentId as any,
        userId: user.id,
        isApproved: false
      });
      setConfirmAction(null);
    } catch (error) {
      console.error('Failed to reject comment:', error);
    }
  };

  const handleDelete = async (commentId: string) => {
    if (!user) return;
    
    try {
      await deleteComment({
        commentId: commentId as any,
        userId: user.id
      });
      setConfirmAction(null);
    } catch (error) {
      console.error('Failed to delete comment:', error);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!comments) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-6 bg-jobblogg-neutral rounded w-1/3"></div>
        <div className="space-y-3">
          {[1, 2].map((i) => (
            <div key={i} className="h-24 bg-jobblogg-neutral rounded-xl"></div>
          ))}
        </div>
      </div>
    );
  }

  const pendingComments = comments.filter(c => c.isApproved === false || c.isApproved === undefined);
  const approvedComments = comments.filter(c => c.isApproved === true);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Heading3>Kundekommentarer</Heading3>
        {pendingComments.length > 0 && (
          <div className="bg-jobblogg-warning-soft text-jobblogg-warning px-3 py-1 rounded-full text-sm font-medium">
            {pendingComments.length} venter på godkjenning
          </div>
        )}
      </div>

      {comments.length === 0 ? (
        <div className="text-center py-8">
          <TextMuted>Ingen kundekommentarer ennå</TextMuted>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Pending Comments */}
          {pendingComments.length > 0 && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <TextStrong className="text-jobblogg-warning">
                  Venter på godkjenning ({pendingComments.length})
                </TextStrong>
              </div>
              <div className="space-y-4">
                {pendingComments.map((comment) => (
                  <div key={comment._id} className="bg-jobblogg-warning-soft border border-jobblogg-warning rounded-xl p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <TextStrong>{comment.customerName}</TextStrong>
                        {comment.customerEmail && (
                          <TextMuted className="text-sm">{comment.customerEmail}</TextMuted>
                        )}
                      </div>
                      <TextMuted className="text-sm">
                        {formatDate(comment.createdAt)}
                      </TextMuted>
                    </div>
                    
                    <TextMedium className="mb-4">{comment.comment}</TextMedium>
                    
                    <div className="flex items-center gap-3">
                      <PrimaryButton
                        size="sm"
                        onClick={() => setConfirmAction({
                          type: 'approve',
                          commentId: comment._id,
                          customerName: comment.customerName
                        })}
                      >
                        Godkjenn
                      </PrimaryButton>
                      <SecondaryButton
                        size="sm"
                        onClick={() => setConfirmAction({
                          type: 'reject',
                          commentId: comment._id,
                          customerName: comment.customerName
                        })}
                      >
                        Avvis
                      </SecondaryButton>
                      <SecondaryButton
                        size="sm"
                        variant="danger"
                        onClick={() => setConfirmAction({
                          type: 'delete',
                          commentId: comment._id,
                          customerName: comment.customerName
                        })}
                      >
                        Slett
                      </SecondaryButton>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Approved Comments */}
          {approvedComments.length > 0 && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <TextStrong className="text-jobblogg-success">
                  Godkjente kommentarer ({approvedComments.length})
                </TextStrong>
              </div>
              <div className="space-y-4">
                {approvedComments.map((comment) => (
                  <div key={comment._id} className="bg-jobblogg-success-soft border border-jobblogg-success rounded-xl p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <TextStrong>{comment.customerName}</TextStrong>
                        {comment.customerEmail && (
                          <TextMuted className="text-sm">{comment.customerEmail}</TextMuted>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="bg-jobblogg-success text-white px-2 py-1 rounded text-xs font-medium mb-1">
                          Godkjent
                        </div>
                        <TextMuted className="text-sm">
                          {formatDate(comment.createdAt)}
                        </TextMuted>
                      </div>
                    </div>
                    
                    <TextMedium className="mb-4">{comment.comment}</TextMedium>
                    
                    <div className="flex items-center gap-3">
                      <SecondaryButton
                        size="sm"
                        onClick={() => setConfirmAction({
                          type: 'reject',
                          commentId: comment._id,
                          customerName: comment.customerName
                        })}
                      >
                        Skjul
                      </SecondaryButton>
                      <SecondaryButton
                        size="sm"
                        variant="danger"
                        onClick={() => setConfirmAction({
                          type: 'delete',
                          commentId: comment._id,
                          customerName: comment.customerName
                        })}
                      >
                        Slett
                      </SecondaryButton>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Confirmation Dialog */}
      {confirmAction && (
        <ConfirmDialog
          isOpen={true}
          onClose={() => setConfirmAction(null)}
          onConfirm={() => {
            if (confirmAction.type === 'approve') {
              handleApprove(confirmAction.commentId);
            } else if (confirmAction.type === 'reject') {
              handleReject(confirmAction.commentId);
            } else if (confirmAction.type === 'delete') {
              handleDelete(confirmAction.commentId);
            }
          }}
          title={
            confirmAction.type === 'approve' ? 'Godkjenn kommentar' :
            confirmAction.type === 'reject' ? 'Avvis kommentar' :
            'Slett kommentar'
          }
          message={
            confirmAction.type === 'approve' 
              ? `Er du sikker på at du vil godkjenne kommentaren fra ${confirmAction.customerName}? Den vil bli synlig for alle som har tilgang til prosjektet.`
              : confirmAction.type === 'reject'
              ? `Er du sikker på at du vil avvise kommentaren fra ${confirmAction.customerName}? Den vil ikke være synlig for kunder.`
              : `Er du sikker på at du vil slette kommentaren fra ${confirmAction.customerName}? Denne handlingen kan ikke angres.`
          }
          confirmText={
            confirmAction.type === 'approve' ? 'Godkjenn' :
            confirmAction.type === 'reject' ? 'Avvis' :
            'Slett'
          }
          variant={confirmAction.type === 'delete' ? 'danger' : 'primary'}
        />
      )}
    </div>
  );
};
