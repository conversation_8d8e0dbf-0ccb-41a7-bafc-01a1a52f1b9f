import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TextInput, TextArea, PrimaryButton, TextMedium, TextMuted, TextStrong } from '../ui';

interface CustomerCommentFormProps {
  projectId: string;
  sharedId: string;
}

export const CustomerCommentForm: React.FC<CustomerCommentFormProps> = ({ projectId, sharedId }) => {
  const [customerName, setCustomerName] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const addComment = useMutation(api.customerComments.addComment);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!customerName.trim() || !comment.trim()) {
      setSubmitError('Navn og kommentar er påkrevd');
      return;
    }

    if (comment.length > 1000) {
      setSubmitError('Kommentar kan ikke være lengre enn 1000 tegn');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      await addComment({
        projectId: projectId as any,
        sharedId,
        customerName: customerName.trim(),
        customerEmail: customerEmail.trim() || undefined,
        comment: comment.trim()
      });

      // Reset form
      setCustomerName('');
      setCustomerEmail('');
      setComment('');
      setSubmitSuccess(true);
      
      // Hide success message after 5 seconds
      setTimeout(() => setSubmitSuccess(false), 5000);
    } catch (error) {
      console.error('Failed to submit comment:', error);
      setSubmitError(error instanceof Error ? error.message : 'Kunne ikke sende kommentar');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitSuccess) {
    return (
      <div className="bg-jobblogg-success-soft border border-jobblogg-success rounded-xl p-6 text-center">
        <div className="w-12 h-12 mx-auto mb-4 bg-jobblogg-success rounded-full flex items-center justify-center">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <TextStrong className="text-jobblogg-success text-lg mb-2 block">
          Kommentar sendt!
        </TextStrong>
        <TextMuted>
          Takk for din kommentar! Den er sendt til kontraktøren for godkjenning og vil vises her når den er godkjent. Du kan fortsette å følge prosjektet på denne siden.
        </TextMuted>
      </div>
    );
  }

  return (
    <div className="bg-jobblogg-neutral rounded-xl p-6">
      <div className="mb-6">
        <TextStrong className="text-lg mb-2 block">Legg til kommentar</TextStrong>
        <TextMuted>
          Del dine tanker eller spørsmål om prosjektet. Kommentarer må godkjennes av kontraktøren før de vises offentlig.
        </TextMuted>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Customer Name */}
        <div>
          <label htmlFor="customerName" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
            Navn *
          </label>
          <TextInput
            id="customerName"
            type="text"
            value={customerName}
            onChange={(e) => setCustomerName(e.target.value)}
            placeholder="Ditt navn"
            required
            maxLength={100}
            disabled={isSubmitting}
          />
        </div>

        {/* Customer Email (Optional) */}
        <div>
          <label htmlFor="customerEmail" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
            E-post (valgfritt)
          </label>
          <TextInput
            id="customerEmail"
            type="email"
            value={customerEmail}
            onChange={(e) => setCustomerEmail(e.target.value)}
            placeholder="<EMAIL>"
            maxLength={100}
            disabled={isSubmitting}
          />
          <TextMuted className="text-sm mt-1">
            E-post brukes kun for å kontakte deg ved behov
          </TextMuted>
        </div>

        {/* Comment */}
        <div>
          <label htmlFor="comment" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
            Kommentar *
          </label>
          <TextArea
            id="comment"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Skriv din kommentar her..."
            rows={4}
            required
            maxLength={1000}
            disabled={isSubmitting}
          />
          <div className="flex justify-between items-center mt-1">
            <TextMuted className="text-sm">
              Maks 1000 tegn
            </TextMuted>
            <TextMuted className="text-sm">
              {comment.length}/1000
            </TextMuted>
          </div>
        </div>

        {/* Error Message */}
        {submitError && (
          <div className="bg-jobblogg-error-soft border border-jobblogg-error rounded-lg p-3">
            <TextMedium className="text-jobblogg-error">
              {submitError}
            </TextMedium>
          </div>
        )}

        {/* Submit Button */}
        <div className="pt-2">
          <PrimaryButton
            type="submit"
            disabled={isSubmitting || !customerName.trim() || !comment.trim()}
            className="w-full sm:w-auto"
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sender...
              </>
            ) : (
              'Send kommentar'
            )}
          </PrimaryButton>
        </div>

        {/* Privacy Notice */}
        <div className="bg-jobblogg-neutral-light rounded-lg p-3 mt-4">
          <TextMuted className="text-sm">
            <strong>Personvern:</strong> Informasjonen du oppgir brukes kun for å behandle din kommentar. 
            Kontraktøren kan kontakte deg på oppgitt e-post ved behov.
          </TextMuted>
        </div>
      </form>
    </div>
  );
};
