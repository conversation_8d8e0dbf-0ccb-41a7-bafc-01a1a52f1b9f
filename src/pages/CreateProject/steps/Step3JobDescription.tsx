import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, PrimaryButton, FormError } from '../../../components/ui';

// Local interface definition to avoid import issues
interface WizardFormData {
  projectName: string;
  description: string;
  customerName: string;
  customerType: 'privat' | 'firma';
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  orgNumber: string;
  notes: string;
  jobDescription: string;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;
}

interface Step3JobDescriptionProps {
  formData: WizardFormData;
  updateFormData: (updates: Partial<WizardFormData>) => void;
  errors: { [key: string]: string };
  onPrevious: () => void;
  isLoading: boolean;
  setIsLoading: (value: boolean) => void;
  setErrors: (errors: { [key: string]: string }) => void;
  updateProjectJobData: any;
  createProject: any;
  createCustomer: any;
  user: any;
  clearSavedData: () => void;
  setShowSuccess: (value: boolean) => void;
  navigate: (path: string) => void;
  createdProjectId: string | null;
  useExistingCustomer: boolean;
  selectedCustomerId: string;
}

export const Step3JobDescription: React.FC<Step3JobDescriptionProps> = ({
  formData,
  updateFormData,
  errors,
  onPrevious,
  isLoading,
  setIsLoading,
  setErrors,
  updateProjectJobData,
  createProject,
  createCustomer,
  user,
  clearSavedData,
  setShowSuccess,
  navigate,
  createdProjectId,
  useExistingCustomer,
  selectedCustomerId
}) => {
  // Image handling state
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);

  // Handle image selection from gallery
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    // Create preview URLs
    const newPreviews = files.map(file => URL.createObjectURL(file));

    setSelectedImages(prev => [...prev, ...files]);
    setImagePreviews(prev => [...prev, ...newPreviews]);

    // Reset input
    if (event.target) {
      event.target.value = '';
    }
  };

  // Handle camera capture
  const handleCameraCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleImageSelect(event);
  };

  // Remove image
  const removeImage = (index: number) => {
    // Revoke object URL to prevent memory leaks
    URL.revokeObjectURL(imagePreviews[index]);

    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  // Cleanup object URLs on unmount
  useEffect(() => {
    return () => {
      imagePreviews.forEach(url => URL.revokeObjectURL(url));
    };
  }, [imagePreviews]);

  // Create complete project with job data
  const handleCreateCompleteProject = async () => {
    setIsLoading(true);
    setErrors({});

    try {
      if (!user?.id) {
        console.error('User not authenticated');
        setIsLoading(false);
        return;
      }

      let projectId = createdProjectId;

      // If no project was created in step 2, create it now
      if (!projectId) {
        let customerId;

        if (useExistingCustomer) {
          customerId = selectedCustomerId;
        } else {
          // Create new customer
          customerId = await createCustomer({
            name: formData.customerName.trim(),
            type: formData.customerType,
            contactPerson: formData.contactPerson.trim() || undefined,
            phone: formData.phone.trim() || undefined,
            email: formData.email.trim() || undefined,
            address: formData.address.trim(),
            orgNumber: formData.orgNumber.trim() || undefined,
            notes: formData.notes.trim() || undefined,
            userId: user.id
          });
        }

        // Create project with customer reference
        projectId = await createProject({
          name: formData.projectName.trim(),
          description: formData.description.trim(),
          userId: user.id,
          customerId: customerId as any
        });
      }

      // Add job data to the project if any job information is provided
      const hasJobData = formData.jobDescription || formData.accessNotes ||
                        formData.equipmentNeeds || formData.unresolvedQuestions ||
                        formData.personalNotes;

      if (hasJobData && projectId) {
        await updateProjectJobData({
          projectId: projectId as any,
          userId: user.id,
          jobData: {
            jobDescription: formData.jobDescription,
            photos: selectedImages.map(file => ({
              name: file.name,
              size: file.size,
              type: file.type,
              url: URL.createObjectURL(file) // Temporary URL for preview
            })),
            accessNotes: formData.accessNotes,
            equipmentNeeds: formData.equipmentNeeds,
            unresolvedQuestions: formData.unresolvedQuestions,
            personalNotes: formData.personalNotes
          }
        });
      }

      // Clear saved data and show success
      clearSavedData();
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        navigate('/');
      }, 2000);

    } catch (error) {
      console.error('Error creating complete project:', error);
      setErrors({ general: 'Det oppstod en feil ved opprettelse av prosjektet. Prøv igjen.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Job Description */}
      <TextArea
        label="Jobbeskrivelse"
        placeholder="Beskriv detaljert hva som skal gjøres i dette prosjektet..."
        fullWidth
        rows={4}
        value={formData.jobDescription}
        onChange={(e) => updateFormData({ jobDescription: e.target.value })}
        helperText="💡 Tips: Vær så spesifikk som mulig for å unngå misforståelser"
      />

      {/* Access Notes */}
      <TextArea
        label="Tilgangsnotater"
        placeholder="Informasjon om tilgang til arbeidsstedet, nøkler, koder, etc..."
        fullWidth
        rows={3}
        value={formData.accessNotes}
        onChange={(e) => updateFormData({ accessNotes: e.target.value })}
        helperText="F.eks. 'Nøkkel under blomsterpotten', 'Ring på dørklokka', 'Kode til port: 1234'"
      />

      {/* Equipment Needs */}
      <TextArea
        label="Utstyrsbehov"
        placeholder="Liste over verktøy, materialer eller utstyr som trengs..."
        fullWidth
        rows={3}
        value={formData.equipmentNeeds}
        onChange={(e) => updateFormData({ equipmentNeeds: e.target.value })}
        helperText="F.eks. 'Borhammer', 'Stige 3m', 'Maling - hvit'"
      />

      {/* Unresolved Questions */}
      <TextArea
        label="Uavklarte spørsmål"
        placeholder="Spørsmål som må avklares med kunden før eller under jobben..."
        fullWidth
        rows={3}
        value={formData.unresolvedQuestions}
        onChange={(e) => updateFormData({ unresolvedQuestions: e.target.value })}
        helperText="F.eks. 'Hvilken farge på flisene?', 'Skal vi male taket også?'"
      />

      {/* Personal Notes */}
      <TextArea
        label="Personlige notater"
        placeholder="Dine egne notater og påminnelser for prosjektet..."
        fullWidth
        rows={3}
        value={formData.personalNotes}
        onChange={(e) => updateFormData({ personalNotes: e.target.value })}
        helperText="Private notater som kun du ser"
      />

      {/* Image Capture Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-jobblogg-text-strong">Bilder</h3>
          <div className="flex gap-2">
            {/* Camera Button */}
            <button
              type="button"
              onClick={() => cameraInputRef.current?.click()}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-jobblogg-primary bg-jobblogg-primary-soft border border-jobblogg-primary/20 rounded-lg hover:bg-jobblogg-primary/10 focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 transition-colors duration-200"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Ta bilde
            </button>

            {/* Gallery Button */}
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-jobblogg-accent bg-jobblogg-accent-soft border border-jobblogg-accent/20 rounded-lg hover:bg-jobblogg-accent/10 focus:outline-none focus:ring-2 focus:ring-jobblogg-accent/20 transition-colors duration-200"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              Velg bilder
            </button>
          </div>
        </div>

        {/* Hidden File Inputs */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={handleImageSelect}
          className="hidden"
        />
        <input
          ref={cameraInputRef}
          type="file"
          accept="image/*"
          capture="environment"
          onChange={handleCameraCapture}
          className="hidden"
        />

        {/* Image Previews */}
        {imagePreviews.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {imagePreviews.map((preview, index) => (
              <div key={index} className="relative group">
                <img
                  src={preview}
                  alt={`Forhåndsvisning ${index + 1}`}
                  className="w-full h-24 object-cover rounded-lg border border-jobblogg-border"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-jobblogg-error text-white rounded-full flex items-center justify-center text-xs hover:bg-jobblogg-error-dark transition-colors duration-200 opacity-0 group-hover:opacity-100"
                  aria-label="Fjern bilde"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}

        {imagePreviews.length === 0 && (
          <div className="text-center py-8 border-2 border-dashed border-jobblogg-border rounded-lg bg-jobblogg-background-soft">
            <svg className="w-12 h-12 mx-auto text-jobblogg-text-muted mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-jobblogg-text-muted text-sm">
              Ingen bilder valgt. Bruk knappene ovenfor for å legge til bilder.
            </p>
          </div>
        )}
      </div>

      {/* General Error */}
      {errors.general && <FormError message={errors.general} />}

      {/* Navigation */}
      <div className="flex justify-between pt-4">
        <PrimaryButton
          variant="secondary"
          onClick={onPrevious}
          size="large"
          className="min-w-[120px]"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Tilbake
        </PrimaryButton>

        <PrimaryButton
          onClick={handleCreateCompleteProject}
          disabled={isLoading}
          loading={isLoading}
          size="large"
          className="min-w-[160px]"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          Opprett Prosjekt
        </PrimaryButton>
      </div>

      {/* Completion Summary */}
      <div className="bg-jobblogg-primary-soft rounded-lg p-4 mt-6">
        <h4 className="font-semibold text-jobblogg-text-strong mb-2">📋 Prosjektsammendrag</h4>
        <div className="space-y-1 text-sm text-jobblogg-text-muted">
          <p><strong>Prosjekt:</strong> {formData.projectName}</p>
          <p><strong>Kunde:</strong> {formData.customerName || 'Eksisterende kunde'}</p>
          <p><strong>Type:</strong> {formData.customerType === 'firma' ? 'Firma' : 'Privat'}</p>
          {formData.description && <p><strong>Beskrivelse:</strong> {formData.description.substring(0, 100)}{formData.description.length > 100 ? '...' : ''}</p>}
        </div>
      </div>
    </div>
  );
};

export default Step3JobDescription;
