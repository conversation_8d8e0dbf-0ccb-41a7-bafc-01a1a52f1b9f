import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading1, Heading2, TextMedium, TextMuted, TextStrong, EmptyState } from '../../components/ui';
import { CustomerCommentForm } from '../../components/CustomerCommentForm';
import { ThreadedCommentForm } from '../../components/ThreadedCommentForm';
import { ThreadedConversation } from '../../components/ThreadedConversation';
import { EditHistoryModal } from '../../components/EditHistoryModal';

const SharedProject: React.FC = () => {
  const { sharedId } = useParams<{ sharedId: string }>();
  const [showEditHistory, setShowEditHistory] = useState<string | null>(null);

  // Fetch shared project data
  const project = useQuery(
    api.projects.getBySharedId,
    sharedId ? { sharedId } : "skip"
  );

  // Fetch log entries for shared project
  const logEntries = useQuery(
    api.logEntries.getBySharedProject,
    sharedId ? { sharedId } : "skip"
  );

  // Fetch all customer comments (legacy)
  const customerComments = useQuery(
    api.customerComments.getForPublicView,
    project ? { projectId: project._id } : "skip"
  );

  // Fetch threaded conversations
  const threadedComments = useQuery(
    api.customerComments.getThreadedForPublicView,
    project ? { projectId: project._id } : "skip"
  );

  // Edit history handlers
  const handleShowEditHistory = (entryId: string) => {
    setShowEditHistory(entryId);
  };

  const handleCloseEditHistory = () => {
    setShowEditHistory(null);
  };

  // Loading state
  if (project === undefined || logEntries === undefined) {
    return (
      <PageLayout>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-jobblogg-neutral rounded-lg w-1/2"></div>
          <div className="h-4 bg-jobblogg-neutral rounded w-3/4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
        </div>
      </PageLayout>
    );
  }

  // Project not found or not shared
  if (!project) {
    return (
      <PageLayout>
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-6 bg-jobblogg-error-soft rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <Heading2 className="mb-4">Prosjekt ikke funnet</Heading2>
          <TextMuted className="text-lg">
            Dette prosjektet eksisterer ikke eller er ikke lenger delt offentlig.
          </TextMuted>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="space-y-8">
        {/* Project Header */}
        <div className="text-center space-y-4 animate-slide-up">
          <div className="w-16 h-16 mx-auto bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 8h5a2 2 0 002-2V9a2 2 0 00-2-2H9a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <Heading1 className="bg-gradient-to-r from-jobblogg-primary to-jobblogg-accent bg-clip-text text-transparent">
            {project.name}
          </Heading1>
          <TextMedium className="text-lg max-w-2xl mx-auto">
            {project.description}
          </TextMedium>
          
          {/* Customer Info */}
          {project.customer && (
            <div className="bg-jobblogg-neutral rounded-xl p-4 max-w-md mx-auto">
              <TextStrong className="block mb-1">Kunde</TextStrong>
              <TextMedium>{project.customer.name}</TextMedium>
              {project.customer.address && (
                <TextMuted className="text-sm">{project.customer.address}</TextMuted>
              )}
            </div>
          )}
        </div>

        {/* Project Log Entries */}
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <Heading2>Prosjektlogg</Heading2>
            <div className="flex items-center gap-2 text-jobblogg-text-muted">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span className="text-sm">{logEntries?.length || 0} oppføringer</span>
            </div>
          </div>

          {logEntries && logEntries.length > 0 ? (
            <div className="space-y-6">
              {logEntries.map((entry, index) => (
                <div
                  key={entry._id}
                  className="bg-jobblogg-neutral rounded-xl p-6 card-hover animate-slide-up"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="flex flex-col gap-4">
                    <div className="flex justify-between items-start gap-4">
                      <div className="flex-1">
                        <div className="flex items-start gap-2 mb-2">
                          <TextMedium className="leading-relaxed flex-1">{entry.description}</TextMedium>
                          {entry.isEdited && (
                            <span className="px-2 py-1 bg-jobblogg-primary-soft text-jobblogg-primary text-xs font-medium rounded-full flex-shrink-0">
                              Redigert
                            </span>
                          )}
                        </div>
                        {entry.isEdited && (
                          <button
                            onClick={() => handleShowEditHistory(entry._id)}
                            className="text-jobblogg-primary hover:text-jobblogg-primary-dark text-sm font-medium transition-colors duration-200 flex items-center gap-1"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Se endringshistorikk
                          </button>
                        )}
                      </div>
                      <div className="flex items-center gap-2 text-body-small flex-shrink-0">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span>
                          {new Date(entry.createdAt).toLocaleDateString('nb-NO', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                    </div>
                    {entry.imageUrl && (
                      <div className="mt-4">
                        <img
                          src={entry.imageUrl}
                          alt="Prosjektbilde"
                          className="rounded-xl w-full h-auto max-h-96 object-cover shadow-md hover:shadow-lg transition-shadow duration-200"
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <EmptyState
              icon={
                <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              }
              title="Ingen logger ennå"
              description="Dette prosjektet har ingen logger ennå. Kom tilbake senere for oppdateringer! 📝"
            />
          )}
        </div>

        {/* Customer Comments Section */}
        {project.shareSettings?.allowCustomerComments && (
          <div className="space-y-6">
            <Heading2>Kundekommentarer</Heading2>

            {/* New Threaded Comment Form */}
            <ThreadedCommentForm projectId={project._id} sharedId={sharedId!} />

            {/* Threaded Conversations */}
            {threadedComments && threadedComments.length > 0 && (
              <div className="space-y-6">
                {threadedComments.map((thread) => (
                  <ThreadedConversation
                    key={thread.threadId}
                    thread={thread}
                    projectId={project._id}
                    sharedId={sharedId!}
                    isPublicView={true}
                  />
                ))}
              </div>
            )}

            {/* Legacy Comments (for backward compatibility) */}
            {customerComments && customerComments.length > 0 && (
              <div className="space-y-6">
                <div className="bg-jobblogg-warning-soft border border-jobblogg-warning rounded-lg p-4">
                  <TextMuted className="text-sm">
                    <strong>Eldre kommentarer:</strong> Disse kommentarene bruker det gamle systemet. Nye kommentarer vil bruke det forbedrede samtalesystemet ovenfor.
                  </TextMuted>
                </div>
                {customerComments.map((comment) => (
                  <div key={comment._id} className="bg-white border border-jobblogg-border rounded-xl p-6 opacity-75">
                    {/* Customer Comment */}
                    <div className="mb-4">
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                            <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                          </div>
                          <div>
                            <TextStrong>{comment.customerName}</TextStrong>
                            <div className="text-xs text-jobblogg-text-muted">Kunde (eldre system)</div>
                          </div>
                        </div>
                        <TextMuted className="text-sm">
                          {new Date(comment.createdAt).toLocaleDateString('nb-NO', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </TextMuted>
                      </div>

                      <div className="ml-11">
                        <TextMedium>{comment.comment}</TextMedium>
                      </div>
                    </div>

                    {/* Contractor Reply */}
                    {comment.contractorReply && (
                      <div className="ml-6 pl-4 border-l-2 border-jobblogg-accent bg-jobblogg-accent-soft rounded-r-lg p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex items-center gap-2">
                            <div className="w-6 h-6 bg-jobblogg-accent rounded-full flex items-center justify-center">
                              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H5m14 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2z" />
                              </svg>
                            </div>
                            <div>
                              <TextStrong className="text-jobblogg-accent">Kontraktør</TextStrong>
                              <div className="text-xs text-jobblogg-text-muted">Svar (eldre system)</div>
                            </div>
                          </div>
                          <TextMuted className="text-sm">
                            {new Date(comment.contractorReply.repliedAt).toLocaleDateString('nb-NO', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </TextMuted>
                        </div>
                        <TextMedium>{comment.contractorReply.message}</TextMedium>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Edit History Modal */}
        {showEditHistory && (
          <EditHistoryModal
            entryId={showEditHistory}
            isOpen={true}
            onClose={handleCloseEditHistory}
            isSharedView={true}
            sharedId={sharedId}
          />
        )}
      </div>
    </PageLayout>
  );
};

export default SharedProject;
