import React, { useState, useEffect, useCallback } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading2, Heading3, BodyText, TextMuted, PrimaryButton, EmptyState, ConfirmDialog, TextArea } from '../../components/ui';
import { EditLogEntryForm } from '../../components/EditLogEntryForm';
import { EditHistoryModal } from '../../components/EditHistoryModal';
import { ShareProjectModal } from '../../components/ShareProjectModal';

// Job data interface
interface JobData {
  jobDescription: string;
  photos: Array<{
    url: string;
    note?: string;
    capturedAt?: number;
  }>;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;
}

const ProjectDetail: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const { user } = useUser();
  const navigate = useNavigate();

  // Delete dialog state
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Job information state
  const [isJobSectionExpanded, setIsJobSectionExpanded] = useState(false);
  const [jobData, setJobData] = useState<JobData>({
    jobDescription: '',
    photos: [],
    accessNotes: '',
    equipmentNeeds: '',
    unresolvedQuestions: '',
    personalNotes: ''
  });
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [uploadingImages, setUploadingImages] = useState<Set<string>>(new Set());

  // Edit functionality state
  const [editingEntryId, setEditingEntryId] = useState<string | null>(null);
  const [showEditHistory, setShowEditHistory] = useState<string | null>(null);

  // Sharing functionality state
  const [showShareModal, setShowShareModal] = useState(false);

  // Mutations
  const deleteProject = useMutation(api.projects.deleteProject);
  const updateJobData = useMutation(api.projects.updateProjectJobData);
  const storeJobImage = useMutation(api.projects.storeJobImage);
  const generateUploadUrl = useMutation(api.logEntries.generateUploadUrl);

  // Format date in Norwegian format: DD.MM.YYYY HH:MM
  const formatNorwegianDateTime = (timestamp: number): string => {
    try {
      const date = new Date(timestamp);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Ugyldig dato';
      }

      // Format as DD.MM.YYYY HH:MM
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');

      return `${day}.${month}.${year} ${hours}:${minutes}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Ugyldig dato';
    }
  };

  // Format date for short display (DD. MMM)
  const formatShortDate = (timestamp: number): string => {
    try {
      const date = new Date(timestamp);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Ugyldig dato';
      }

      return date.toLocaleDateString('nb-NO', {
        day: 'numeric',
        month: 'short'
      });
    } catch (error) {
      console.error('Error formatting short date:', error);
      return 'Ugyldig dato';
    }
  };

  // Debounced autosave function
  const debouncedSave = useCallback(
    (() => {
      let timeoutId: NodeJS.Timeout;
      return (data: JobData) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(async () => {
          if (!projectId || !user?.id) return;

          try {
            setIsSaving(true);
            setSaveError(null);
            await updateJobData({
              projectId: projectId as any,
              userId: user.id,
              jobData: data
            });
          } catch (error) {
            console.error('Error saving job data:', error);
            setSaveError('Kunne ikke lagre endringer. Prøv igjen.');
          } finally {
            setIsSaving(false);
          }
        }, 500);
      };
    })(),
    [projectId, user?.id, updateJobData]
  );

  // Handle job data changes with autosave
  const handleJobDataChange = (field: keyof JobData, value: string | JobData['photos']) => {
    const newJobData = { ...jobData, [field]: value };
    setJobData(newJobData);
    debouncedSave(newJobData);
  };

  // Handle photo note update
  const handlePhotoNoteUpdate = (photoIndex: number, note: string) => {
    const updatedPhotos = jobData.photos.map((photo, index) =>
      index === photoIndex ? { ...photo, note } : photo
    );
    handleJobDataChange('photos', updatedPhotos);
  };

  // Handle photo deletion
  const handlePhotoDelete = (photoIndex: number) => {
    const photoToDelete = jobData.photos[photoIndex];

    // Clean up blob URL if it's a temporary preview
    if (photoToDelete.url.startsWith('blob:')) {
      URL.revokeObjectURL(photoToDelete.url);
    }

    const updatedPhotos = jobData.photos.filter((_, index) => index !== photoIndex);
    handleJobDataChange('photos', updatedPhotos);
  };

  // Handle image upload
  const handleImageUpload = async (files: FileList) => {
    if (!files.length || !projectId || !user?.id) return;

    const newUploadingImages = new Set(uploadingImages);

    for (const file of Array.from(files)) {
      if (!file.type.startsWith('image/')) continue;

      const tempId = `temp-${Date.now()}-${Math.random()}`;
      newUploadingImages.add(tempId);
      setUploadingImages(new Set(newUploadingImages));

      try {
        // Create preview URL
        const previewUrl = URL.createObjectURL(file);

        // Add temporary photo to state
        const tempPhoto = {
          url: previewUrl,
          note: '',
          capturedAt: Date.now()
        };

        const updatedPhotos = [...jobData.photos, tempPhoto];
        setJobData(prev => ({ ...prev, photos: updatedPhotos }));

        // Upload to Convex storage
        const uploadUrl = await generateUploadUrl();

        const postUrl = await fetch(uploadUrl, {
          method: 'POST',
          headers: { 'Content-Type': file.type },
          body: file
        });

        const { storageId } = await postUrl.json();

        // Get the actual URL
        const { url } = await storeJobImage({
          projectId: projectId as any,
          userId: user.id,
          storageId
        });

        // Replace temporary photo with actual photo
        const finalPhotos = updatedPhotos.map(photo =>
          photo.url === previewUrl
            ? { ...photo, url }
            : photo
        );

        const newJobData = { ...jobData, photos: finalPhotos };
        setJobData(newJobData);
        debouncedSave(newJobData);

        // Clean up preview URL
        URL.revokeObjectURL(previewUrl);

      } catch (error) {
        console.error('Error uploading image:', error);
        setSaveError('Kunne ikke laste opp bilde. Prøv igjen.');

        // Remove failed upload from photos
        const filteredPhotos = jobData.photos.filter(photo =>
          !photo.url.startsWith('blob:')
        );
        setJobData(prev => ({ ...prev, photos: filteredPhotos }));
      } finally {
        newUploadingImages.delete(tempId);
        setUploadingImages(new Set(newUploadingImages));
      }
    }
  };

  // Fetch project details
  const project = useQuery(
    api.projects.getById,
    projectId ? { projectId: projectId as any } : "skip"
  );

  // Fetch log entries for this project
  const logEntries = useQuery(
    api.logEntries.getByProject,
    projectId && user?.id && project ? {
      projectId: projectId as any,
      userId: user.id
    } : "skip"
  );

  // Handle project deletion
  const handleDeleteProject = async () => {
    if (!projectId || !user?.id) return;

    setIsDeleting(true);
    try {
      await deleteProject({
        projectId: projectId as any,
        userId: user.id
      });

      // Close dialog first
      setShowDeleteDialog(false);
      setIsDeleting(false);

      // Navigate back to dashboard after successful deletion
      // Use setTimeout to ensure state updates are processed
      setTimeout(() => {
        navigate('/', { replace: true });
      }, 100);
    } catch (error) {
      console.error('Feil ved sletting av prosjekt:', error);
      // You could add toast notification here for error handling
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  // Edit functionality handlers
  const handleEditEntry = (entryId: string) => {
    setEditingEntryId(entryId);
  };

  const handleCancelEdit = () => {
    setEditingEntryId(null);
  };

  const handleSaveEdit = () => {
    setEditingEntryId(null);
    // Log entries will be automatically refetched due to Convex reactivity
  };

  const handleShowEditHistory = (entryId: string) => {
    setShowEditHistory(entryId);
  };

  const handleCloseEditHistory = () => {
    setShowEditHistory(null);
  };

  // Initialize job data when project loads
  useEffect(() => {
    console.log('Project loaded:', project);
    console.log('Project jobData:', project?.jobData);
    if (project?.jobData) {
      setJobData(project.jobData);
      setIsJobSectionExpanded(true); // Expand if there's existing data
    }
  }, [project]);

  // Handle navigation away from deleted project
  useEffect(() => {
    // If project query has loaded but project doesn't exist or doesn't belong to user
    if (project === null && projectId && user?.id) {
      // Project was likely deleted, navigate to dashboard
      navigate('/', { replace: true });
    }
  }, [project, projectId, user?.id, navigate]);

  // Modern loading state with skeletons
  if (project === undefined || logEntries === undefined) {
    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          {/* Header Skeleton */}
          <div className="flex items-center gap-4 mb-8">
            <div className="skeleton h-10 w-10 rounded-full"></div>
            <div className="skeleton h-8 w-64"></div>
            <div className="ml-auto skeleton h-10 w-32 rounded-lg"></div>
          </div>

          {/* Project Info Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="bg-base-100 rounded-xl p-8 shadow-lg">
                <div className="skeleton h-8 w-48 mb-4"></div>
                <div className="skeleton h-20 w-full mb-6"></div>
                <div className="flex gap-4">
                  <div className="skeleton h-12 w-32 rounded-lg"></div>
                  <div className="skeleton h-12 w-32 rounded-lg"></div>
                </div>
              </div>
            </div>
            <div>
              <div className="bg-base-100 rounded-xl p-6 shadow-lg">
                <div className="skeleton h-6 w-24 mb-4"></div>
                <div className="space-y-3">
                  <div className="skeleton h-4 w-full"></div>
                  <div className="skeleton h-4 w-3/4"></div>
                  <div className="skeleton h-4 w-1/2"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Gallery Skeleton */}
          <div className="bg-white rounded-xl p-8 shadow-lg border border-jobblogg-border">
            <div className="skeleton h-8 w-32 mb-6"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="skeleton h-48 w-full rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Modern error state for unauthorized/not found
  if (!project || project.userId !== user?.id) {
    return (
      <div className="min-h-screen bg-base-200/30 animate-fade-in">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="flex items-center gap-4 mb-8">
            <Link
              to="/"
              className="btn btn-ghost btn-circle btn-modern hover:bg-jobblogg-primary/10"
              aria-label="Tilbake til oversikt"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
          </div>

          <div className="bg-jobblogg-error-soft border border-jobblogg-error/20 rounded-xl p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-error/20 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <Heading2 className="text-jobblogg-error mb-2">Prosjekt ikke funnet</Heading2>
            <BodyText className="text-jobblogg-error/80 mb-6">
              Dette prosjektet eksisterer ikke eller du har ikke tilgang til det.
            </BodyText>
            <PrimaryButton onClick={() => navigate('/')}>
              Tilbake til oversikt
            </PrimaryButton>
          </div>
        </div>
      </div>
    );
  }

  // Sort log entries by creation date (newest first)
  const sortedLogEntries = logEntries.sort((a, b) => b.createdAt - a.createdAt);

  return (
    <PageLayout
      title={project.name}
      showBackButton
      backUrl="/"
      headerActions={
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 px-3 py-1.5 bg-jobblogg-primary-soft text-jobblogg-primary rounded-full text-sm font-medium">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Kontraktørvisning
          </div>
          <TextMuted className="text-lg">
            🔧 Intern arbeidsdetaljer og dokumentasjon
          </TextMuted>
          <PrimaryButton
            onClick={() => setShowShareModal(true)}
            variant="secondary"
            className="flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
            Del prosjekt
          </PrimaryButton>
          <PrimaryButton onClick={() => navigate(`/project/${projectId}`)}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Legg til bilde
          </PrimaryButton>
        </div>
      }
    >

        {/* Modern Project Information Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Main Project Info */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-lg border border-jobblogg-border p-8 animate-slide-up">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-jobblogg-primary/10 to-jobblogg-accent/10 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <Heading2>Prosjektinformasjon</Heading2>
              </div>

              <div className="space-y-6">
                <div>
                  <Heading3 className="mb-3 flex items-center gap-2">
                    <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                    </svg>
                    Beskrivelse
                  </Heading3>
                  <div className="bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border">
                    <BodyText className="leading-relaxed">
                      {project.description || (
                        <TextMuted className="italic">
                          Ingen beskrivelse tilgjengelig. Du kan legge til en beskrivelse ved å redigere prosjektet.
                        </TextMuted>
                      )}
                    </BodyText>
                  </div>
                </div>



                {/* Job Information Section */}
                <div>
                  {console.log('Rendering job section, expanded:', isJobSectionExpanded)}
                  <button
                    onClick={() => {
                      console.log('Job section button clicked, current state:', isJobSectionExpanded);
                      setIsJobSectionExpanded(!isJobSectionExpanded);
                    }}
                    className="w-full flex items-center justify-between p-3 bg-jobblogg-neutral rounded-lg border border-jobblogg-border hover:bg-jobblogg-primary-soft transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/30"
                    aria-expanded={isJobSectionExpanded}
                    aria-controls="job-information-content"
                  >
                    <div className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                      </svg>
                      <Heading3 className="text-jobblogg-text-strong">
                        Jobbinformasjon
                      </Heading3>
                      {isSaving && (
                        <div className="flex items-center gap-1 text-jobblogg-text-muted">
                          <svg className="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                          <span className="text-xs">Lagrer...</span>
                        </div>
                      )}
                    </div>
                    <svg
                      className={`w-5 h-5 text-jobblogg-text-muted transition-transform duration-200 ${isJobSectionExpanded ? 'rotate-180' : ''}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {isJobSectionExpanded && (
                    <div
                      id="job-information-content"
                      className="mt-4 space-y-6 bg-white rounded-lg p-6 border border-jobblogg-border"
                    >
                      {saveError && (
                        <div className="bg-jobblogg-error-soft border border-jobblogg-error/20 rounded-lg p-3 flex items-center gap-2">
                          <svg className="w-4 h-4 text-jobblogg-error flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          <span className="text-sm text-jobblogg-error">{saveError}</span>
                        </div>
                      )}

                      {/* Job Description */}
                      <div>
                        <TextArea
                          label="Hva skal gjøres?"
                          placeholder="Beskriv jobben som skal utføres..."
                          value={jobData.jobDescription}
                          onChange={(e) => handleJobDataChange('jobDescription', e.target.value)}
                          rows={4}
                          fullWidth
                          className="min-h-[100px]"
                        />
                      </div>

                      {/* Access Notes */}
                      <div>
                        <TextArea
                          label="Tilkomst og forhold"
                          placeholder="Beskriv tilkomst til arbeidsstedet, parkeringsmuligheter, nøkler, etc..."
                          value={jobData.accessNotes}
                          onChange={(e) => handleJobDataChange('accessNotes', e.target.value)}
                          rows={3}
                          fullWidth
                        />
                      </div>

                      {/* Equipment Needs */}
                      <div>
                        <TextArea
                          label="Hva må medbringes?"
                          placeholder="Liste over verktøy, materialer og utstyr som trengs..."
                          value={jobData.equipmentNeeds}
                          onChange={(e) => handleJobDataChange('equipmentNeeds', e.target.value)}
                          rows={3}
                          fullWidth
                        />
                      </div>

                      {/* Unresolved Questions */}
                      <div>
                        <TextArea
                          label="Hva må avklares?"
                          placeholder="Spørsmål som må avklares med kunden eller andre..."
                          value={jobData.unresolvedQuestions}
                          onChange={(e) => handleJobDataChange('unresolvedQuestions', e.target.value)}
                          rows={3}
                          fullWidth
                        />
                      </div>

                      {/* Personal Notes */}
                      <div>
                        <TextArea
                          label="Egne notater"
                          placeholder="Dine personlige notater og observasjoner..."
                          value={jobData.personalNotes}
                          onChange={(e) => handleJobDataChange('personalNotes', e.target.value)}
                          rows={3}
                          fullWidth
                        />
                      </div>

                      {/* Photo Upload Section */}
                      <div>
                        <label className="block text-sm font-medium mb-3 text-jobblogg-text-strong">
                          Bilder fra befaring
                        </label>

                        {/* Upload Button */}
                        <div className="mb-4">
                          <input
                            type="file"
                            id="job-photo-upload"
                            multiple
                            accept="image/*"
                            capture="environment"
                            onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
                            className="hidden"
                            aria-describedby="upload-help"
                          />
                          <label
                            htmlFor="job-photo-upload"
                            className="
                              inline-flex items-center gap-2 px-4 py-3
                              bg-jobblogg-primary text-white rounded-lg
                              hover:bg-jobblogg-primary-dark transition-colors duration-200
                              focus-within:ring-2 focus-within:ring-jobblogg-primary/30
                              cursor-pointer min-h-[44px]
                            "
                            role="button"
                            tabIndex={0}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' || e.key === ' ') {
                                e.preventDefault();
                                document.getElementById('job-photo-upload')?.click();
                              }
                            }}
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            Ta bilde / Last opp
                          </label>
                          <p id="upload-help" className="mt-1 text-xs text-jobblogg-text-muted">
                            Støtter JPG, PNG og andre bildeformater. Flere bilder kan velges samtidig.
                          </p>
                          {uploadingImages.size > 0 && (
                            <div
                              className="mt-2 text-sm text-jobblogg-text-muted flex items-center gap-1"
                              aria-live="polite"
                              role="status"
                            >
                              <svg className="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              </svg>
                              <span>Laster opp {uploadingImages.size} bilde{uploadingImages.size > 1 ? 'r' : ''}...</span>
                            </div>
                          )}
                        </div>

                        {/* Photo Grid */}
                        {jobData.photos.length > 0 && (
                          <div
                            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
                            role="grid"
                            aria-label="Bilder fra befaring"
                          >
                            {jobData.photos.map((photo, index) => (
                              <div
                                key={index}
                                className="relative bg-jobblogg-neutral rounded-lg overflow-hidden border border-jobblogg-border"
                                role="gridcell"
                              >
                                <div className="aspect-square relative">
                                  <img
                                    src={photo.url}
                                    alt={photo.note ? `Befaring bilde ${index + 1}: ${photo.note}` : `Befaring bilde ${index + 1}`}
                                    className="w-full h-full object-cover"
                                    loading="lazy"
                                  />

                                  {/* Delete Button */}
                                  <button
                                    onClick={() => handlePhotoDelete(index)}
                                    className="
                                      absolute top-2 right-2 p-1.5
                                      bg-jobblogg-error text-white rounded-full
                                      hover:bg-jobblogg-error-dark transition-colors duration-200
                                      focus:outline-none focus:ring-2 focus:ring-jobblogg-error/30
                                      min-h-[44px] min-w-[44px] flex items-center justify-center
                                    "
                                    aria-label={`Slett bilde ${index + 1}${photo.note ? `: ${photo.note}` : ''}`}
                                    title={`Slett bilde ${index + 1}`}
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                  </button>
                                </div>

                                {/* Photo Note */}
                                <div className="p-3">
                                  <TextArea
                                    label={`Kommentar til bilde ${index + 1}`}
                                    placeholder="Legg til kommentar til bildet..."
                                    value={photo.note || ''}
                                    onChange={(e) => handlePhotoNoteUpdate(index, e.target.value)}
                                    rows={2}
                                    fullWidth
                                    size="small"
                                    aria-describedby={photo.capturedAt ? `photo-${index}-timestamp` : undefined}
                                  />
                                  {photo.capturedAt && (
                                    <div
                                      id={`photo-${index}-timestamp`}
                                      className="mt-2 text-xs text-jobblogg-text-muted"
                                      aria-label={`Tatt ${formatNorwegianDateTime(photo.capturedAt)}`}
                                    >
                                      {formatNorwegianDateTime(photo.capturedAt)}
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}

                        {jobData.photos.length === 0 && (
                          <div className="text-center py-8 text-jobblogg-text-muted">
                            <svg className="w-12 h-12 mx-auto mb-3 text-jobblogg-text-muted/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <p className="text-sm">Ingen bilder lagt til ennå</p>
                            <p className="text-xs mt-1">Ta bilder av arbeidsstedet for dokumentasjon</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <Heading3 className="mb-3 flex items-center gap-2">
                    <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Opprettet
                  </Heading3>
                  <div className="bg-jobblogg-neutral rounded-lg p-4">
                    <BodyText>
                      {formatNorwegianDateTime(project.createdAt)}
                    </BodyText>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3 mt-8 pt-6 border-t border-jobblogg-border">
                <PrimaryButton onClick={() => navigate(`/project/${projectId}`)} className="flex-1 sm:flex-none">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Legg til bilde
                </PrimaryButton>
                <PrimaryButton variant="outline" className="flex-1 sm:flex-none">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Rediger prosjekt
                </PrimaryButton>
                <button
                  onClick={() => setShowDeleteDialog(true)}
                  className="
                    flex-1 sm:flex-none px-4 py-2 rounded-lg font-medium
                    text-jobblogg-error hover:text-white
                    border border-jobblogg-error hover:bg-jobblogg-error
                    transition-all duration-200
                    focus:outline-none focus:ring-2 focus:ring-jobblogg-error/30
                    flex items-center justify-center gap-2
                  "
                  aria-label="Slett prosjekt"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Slett prosjekt
                </button>
              </div>
            </div>
          </div>

          {/* Statistics Sidebar */}
          <div>
            <div className="bg-white rounded-xl shadow-lg p-6 animate-slide-up border border-jobblogg-border" style={{ animationDelay: '100ms' }}>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-br from-jobblogg-accent/10 to-jobblogg-accent/10 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <Heading3>Statistikk</Heading3>
              </div>

              <div className="space-y-4">
                <div className="bg-jobblogg-primary-soft rounded-lg p-4 text-center">
                  <div className="text-3xl font-bold text-jobblogg-primary mb-1">
                    {sortedLogEntries.length}
                  </div>
                  <TextMuted className="text-sm">Totalt bilder</TextMuted>
                </div>

                <div className="bg-jobblogg-accent-soft rounded-lg p-4 text-center">
                  <div className="text-lg font-bold text-jobblogg-accent mb-1">
                    {sortedLogEntries.length > 0
                      ? formatShortDate(sortedLogEntries[0].createdAt)
                      : 'Ingen aktivitet'
                    }
                  </div>
                  <TextMuted className="text-sm">Siste aktivitet</TextMuted>
                </div>

                <div className="bg-accent/5 rounded-lg p-4 text-center">
                  <div className="text-lg font-bold text-accent mb-1">
                    {Math.ceil((Date.now() - project.createdAt) / (1000 * 60 * 60 * 24))}
                  </div>
                  <div className="text-sm text-base-content/60">Dager siden oppstart</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Log Entries Section */}
        <div className="bg-white rounded-xl shadow-lg border border-jobblogg-border p-8 animate-slide-up" style={{ animationDelay: '200ms' }}>
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-jobblogg-primary/10 to-jobblogg-primary/10 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <Heading2>📸 Bildesamling</Heading2>
                <TextMuted className="text-sm">Kronologisk oversikt over prosjektets fremgang</TextMuted>
              </div>
            </div>
            {sortedLogEntries.length > 0 && (
              <div className="bg-jobblogg-primary-soft text-jobblogg-primary px-3 py-1 rounded-full text-sm font-medium">
                {sortedLogEntries.length} bilde{sortedLogEntries.length !== 1 ? 'r' : ''}
              </div>
            )}
          </div>

          {sortedLogEntries.length === 0 ? (
            /* Empty State using UI Component */
            <div className="py-16">
              <EmptyState
                title="Ingen bilder lagt til ennå"
                description="Start dokumentering av prosjektet ved å legge til ditt første bilde. Bilder hjelper deg å følge fremgangen og dele resultater! 📷"
                actionLabel="Legg til første bilde"
                onAction={() => navigate(`/project/${projectId}`)}
              />
            </div>
          ) : (
            /* Modern Log Entries Grid */
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {sortedLogEntries.map((entry, index) => {
                const isEditing = editingEntryId === entry._id;
                const isOwner = entry.userId === user?.id;

                if (isEditing && isOwner) {
                  return (
                    <div key={entry._id} className="col-span-full">
                      <EditLogEntryForm
                        entryId={entry._id}
                        currentDescription={entry.description || ''}
                        currentImageUrl={entry.imageUrl}
                        onSave={handleSaveEdit}
                        onCancel={handleCancelEdit}
                      />
                    </div>
                  );
                }

                return (
                  <div
                    key={entry._id}
                    className="bg-jobblogg-neutral rounded-xl overflow-hidden card-hover animate-scale-in group border border-jobblogg-border"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="aspect-video relative overflow-hidden">
                      {entry.imageUrl ? (
                        <img
                          src={entry.imageUrl}
                          alt={entry.description || 'Prosjektbilde'}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full bg-jobblogg-neutral-dark flex items-center justify-center">
                          <svg
                            className="w-12 h-12 text-jobblogg-text-muted"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                        </div>
                      )}
                      <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs">
                        #{sortedLogEntries.length - index}
                      </div>

                      {/* Edit Badge for edited entries */}
                      {entry.isEdited && (
                        <div className="absolute top-3 left-3 bg-jobblogg-accent text-white px-2 py-1 rounded-full text-xs flex items-center gap-1">
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                          Redigert
                        </div>
                      )}
                    </div>

                    <div className="p-4">
                      <p className="text-jobblogg-text-strong leading-relaxed text-sm mb-3">
                        {entry.description || (
                          <span className="text-jobblogg-text-muted italic">Ingen beskrivelse</span>
                        )}
                      </p>

                      <div className="flex items-center justify-between text-xs text-jobblogg-text-muted mb-3">
                        <div className="flex items-center gap-1">
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <span>
                            {formatNorwegianDateTime(entry.createdAt)}
                          </span>
                        </div>
                        {entry.imageUrl && (
                          <div className="flex items-center gap-1 text-jobblogg-accent">
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span>Med bilde</span>
                          </div>
                        )}
                      </div>

                      {/* Edit Controls - Only show for entry owner */}
                      {isOwner && (
                        <div className="flex items-center justify-between pt-2 border-t border-jobblogg-border">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handleEditEntry(entry._id)}
                              className="flex items-center gap-1 px-2 py-1 text-xs text-jobblogg-primary hover:bg-jobblogg-primary-soft rounded transition-colors duration-200"
                              title="Rediger loggføring"
                            >
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                              Rediger
                            </button>

                            {entry.isEdited && entry.editHistory && entry.editHistory.length > 0 && (
                              <button
                                onClick={() => handleShowEditHistory(entry._id)}
                                className="flex items-center gap-1 px-2 py-1 text-xs text-jobblogg-accent hover:bg-jobblogg-accent-soft rounded transition-colors duration-200"
                                title="Vis redigeringshistorikk"
                              >
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Historikk ({entry.editHistory.length})
                              </button>
                            )}
                          </div>

                          {entry.lastEditedAt && (
                            <div className="text-xs text-jobblogg-text-muted">
                              Sist redigert: {formatNorwegianDateTime(entry.lastEditedAt)}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Modern Action Section */}
        {sortedLogEntries.length > 0 && (
          <div className="mt-12 text-center">
            <div className="bg-base-200/30 rounded-xl p-8">
              <Heading3 className="mb-3">
                Fortsett dokumenteringen! 🚀
              </Heading3>
              <TextMuted className="mb-6">
                Legg til flere bilder for å følge prosjektets fremgang over tid
              </TextMuted>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <PrimaryButton
                  onClick={() => navigate(`/project/${projectId}`)}
                  size="lg"
                  className="shadow-lg hover:shadow-xl"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Legg til nytt bilde
                </PrimaryButton>
                <PrimaryButton
                  onClick={() => navigate('/')}
                  variant="outline"
                  size="lg"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
                  </svg>
                  Tilbake til oversikt
                </PrimaryButton>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        <ConfirmDialog
          isOpen={showDeleteDialog}
          title="Slett prosjekt"
          message="Er du sikker på at du vil slette dette prosjektet? Alle bilder og loggoppføringer vil også bli slettet. Denne handlingen kan ikke angres."
          confirmText="Slett prosjekt"
          cancelText="Avbryt"
          isDestructive={true}
          isLoading={isDeleting}
          onConfirm={handleDeleteProject}
          onCancel={() => setShowDeleteDialog(false)}
        />

        {/* Edit History Modal */}
        {showEditHistory && (
          <EditHistoryModal
            entryId={showEditHistory}
            isOpen={true}
            onClose={handleCloseEditHistory}
          />
        )}

        {/* Share Project Modal */}
        {showShareModal && (
          <ShareProjectModal
            isOpen={showShareModal}
            onClose={() => setShowShareModal(false)}
            project={project}
            userId={user?.id || ''}
          />
        )}
    </PageLayout>
  );
};

export default ProjectDetail;
