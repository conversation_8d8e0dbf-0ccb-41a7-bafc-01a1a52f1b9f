import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout } from '../../components/ui/Layout/PageLayout';
import { PrimaryButton } from '../../components/ui/Button/PrimaryButton';
import { EmptyState } from '../../components/ui/EmptyState/EmptyState';

interface UnreadComment {
  _id: string;
  message: string;
  customerName: string;
  customerEmail?: string;
  createdAt: number;
  threadId?: string;
  isRootComment?: boolean;
  project: {
    _id: string;
    name: string;
    description?: string;
  };
}

const UnreadComments: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  
  // Queries
  const unreadComments = useQuery(api.customerComments.getUnreadComments, { 
    userId: user?.id || "" 
  }) as UnreadComment[] | undefined;
  
  // Mutations
  const markAsRead = useMutation(api.customerComments.markAsRead);
  const markAllAsRead = useMutation(api.customerComments.markAllAsRead);
  const addContractorReply = useMutation(api.customerComments.addContractorReply);
  
  // State
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  const [isSubmittingReply, setIsSubmittingReply] = useState(false);
  const [markingAsRead, setMarkingAsRead] = useState<Set<string>>(new Set());

  // Handle mark as read
  const handleMarkAsRead = async (commentId: string) => {
    if (!user?.id) return;

    setMarkingAsRead(prev => new Set(prev).add(commentId));
    try {
      await markAsRead({ commentId, userId: user.id });
    } catch (error) {
      console.error('Error marking comment as read:', error);
    } finally {
      setMarkingAsRead(prev => {
        const newSet = new Set(prev);
        newSet.delete(commentId);
        return newSet;
      });
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    if (!user?.id) return;
    
    try {
      await markAllAsRead({ userId: user.id });
    } catch (error) {
      console.error('Error marking all comments as read:', error);
    }
  };

  // Handle reply submission
  const handleReplySubmit = async (commentId: string) => {
    if (!user?.id || !replyText.trim()) return;

    setIsSubmittingReply(true);
    try {
      await addContractorReply({
        parentCommentId: commentId,
        message: replyText.trim(),
        userId: user.id
      });

      // Clear reply form and mark original comment as read
      setReplyText('');
      setReplyingTo(null);
      await handleMarkAsRead(commentId);
    } catch (error) {
      console.error('Error submitting reply:', error);
    } finally {
      setIsSubmittingReply(false);
    }
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Loading state
  if (unreadComments === undefined) {
    return (
      <PageLayout title="Uleste kommentarer" showBackButton backUrl="/">
        <div className="flex justify-center py-12">
          <div className="w-8 h-8 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      </PageLayout>
    );
  }

  // Empty state
  if (unreadComments.length === 0) {
    return (
      <PageLayout title="Uleste kommentarer" showBackButton backUrl="/">
        <EmptyState
          title="🎉 Ingen uleste kommentarer!"
          description="Du har ingen nye kommentarer fra kunder. Alle kommentarer er lest og besvart."
          actionLabel="Tilbake til dashboard"
          onAction={() => navigate('/')}
        />
      </PageLayout>
    );
  }

  return (
    <PageLayout 
      title="Uleste kommentarer" 
      showBackButton 
      backUrl="/"
      headerActions={
        <PrimaryButton
          variant="outline"
          size="sm"
          onClick={handleMarkAllAsRead}
          disabled={unreadComments.length === 0}
        >
          Marker alle som lest
        </PrimaryButton>
      }
    >
      <div className="space-y-6">
        {/* Summary */}
        <div className="bg-jobblogg-card rounded-xl p-6 border border-jobblogg-border">
          <h2 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
            {unreadComments.length} {unreadComments.length === 1 ? 'ulest kommentar' : 'uleste kommentarer'}
          </h2>
          <p className="text-jobblogg-text-medium">
            Nye kommentarer fra kunder som venter på svar eller bekreftelse.
          </p>
        </div>

        {/* Comments List */}
        <div className="space-y-4">
          {unreadComments.map((comment) => (
            <div
              key={comment._id}
              className="bg-white rounded-xl p-6 border border-jobblogg-border shadow-sm hover:shadow-md transition-shadow"
            >
              {/* Comment Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-semibold text-jobblogg-text-strong">
                      {comment.customerName}
                    </h3>
                    <span className="px-2 py-1 bg-jobblogg-accent-soft text-jobblogg-accent text-xs font-medium rounded-full">
                      {comment.isRootComment ? 'Ny kommentar' : 'Svar'}
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-jobblogg-text-muted">
                    <span>📅 {formatDate(comment.createdAt)}</span>
                    <button
                      onClick={() => navigate(`/project/${comment.project._id}`)}
                      className="text-jobblogg-primary hover:text-jobblogg-primary-dark font-medium"
                    >
                      📁 {comment.project.name}
                    </button>
                  </div>
                </div>
                
                <PrimaryButton
                  variant="outline"
                  size="sm"
                  onClick={() => handleMarkAsRead(comment._id)}
                  disabled={markingAsRead.has(comment._id)}
                >
                  {markingAsRead.has(comment._id) ? 'Markerer...' : 'Marker som lest'}
                </PrimaryButton>
              </div>

              {/* Comment Content */}
              <div className="bg-jobblogg-neutral rounded-lg p-4 mb-4">
                <p className="text-jobblogg-text-strong whitespace-pre-wrap">
                  {comment.message}
                </p>
              </div>

              {/* Reply Section */}
              <div className="border-t border-jobblogg-border pt-4">
                {replyingTo === comment._id ? (
                  <div className="space-y-3">
                    <textarea
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      placeholder="Skriv ditt svar til kunden..."
                      className="w-full p-3 border border-jobblogg-border rounded-lg resize-none focus:ring-2 focus:ring-jobblogg-primary focus:border-transparent"
                      rows={3}
                    />
                    <div className="flex gap-2">
                      <PrimaryButton
                        onClick={() => handleReplySubmit(comment._id)}
                        disabled={!replyText.trim() || isSubmittingReply}
                        size="sm"
                      >
                        {isSubmittingReply ? 'Sender...' : 'Send svar'}
                      </PrimaryButton>
                      <PrimaryButton
                        variant="outline"
                        onClick={() => {
                          setReplyingTo(null);
                          setReplyText('');
                        }}
                        size="sm"
                      >
                        Avbryt
                      </PrimaryButton>
                    </div>
                  </div>
                ) : (
                  <div className="flex gap-2">
                    <PrimaryButton
                      variant="outline"
                      onClick={() => setReplyingTo(comment._id)}
                      size="sm"
                    >
                      💬 Svar på kommentar
                    </PrimaryButton>
                    <PrimaryButton
                      variant="outline"
                      onClick={() => navigate(`/project/${comment.project._id}`)}
                      size="sm"
                    >
                      👁️ Se i prosjekt
                    </PrimaryButton>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </PageLayout>
  );
};

export default UnreadComments;
